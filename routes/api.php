<?php

use App\Http\Controllers\API\AuthController;
use App\Http\Controllers\API\DashboardController;
use App\Http\Controllers\API\VehicleController;
use App\Http\Controllers\API\FleetMonitoringController;
use App\Http\Controllers\API\RouteTrackingController;
use App\Http\Controllers\API\ReportController;
use App\Http\Controllers\API\GeofenceController;
use App\Http\Controllers\DeviceController;
use App\Http\Middleware\SetUserLocale;
use Illuminate\Support\Facades\Route;

Route::middleware(SetUserLocale::class)->group(function () {
    Route::controller(AuthController::class)->group(function () {
        Route::post('login', 'login');
        Route::post('forgot-password', 'forgotPassword');
    });

    Route::middleware(['auth:sanctum'])->group(function () {
        Route::controller(AuthController::class)->group(function () {
            Route::get('profile', 'getProfile');
            Route::get('logout', 'logout');
            Route::post('update-fcm-token', 'updateFcmToken');
            Route::post('update-language', 'updateLanguage');
            Route::get('notifications', 'getNotifications');
        });

        Route::controller(DashboardController::class)->group(function () {
            Route::get('dashboard/stats', 'getStats');
        });

        Route::controller(VehicleController::class)->group(function () {
            Route::get('vehicles', 'index');
            Route::get('vehicles/{id}', 'show');
            Route::get('vehicles/{vehicleId}/events', 'getEvents');
        });

        Route::prefix('fleet')->controller(FleetMonitoringController::class)->group(function () {
            Route::get('vehicles', 'getVehicles');
            Route::get('vehicles-list', 'getVehiclesList');
            Route::get('vehicles/{id}/details', 'getVehicleDetails');
            Route::post('vehicles/history', 'getVehicleHistory');
        });

        Route::prefix('reports')->controller(ReportController::class)->group(function () {
            Route::get('vehicles', 'getVehiclesList');
            Route::post('generate', 'generateReport');
            Route::post('download-daily', 'downloadDailyReport');
            Route::get('export-history', 'getExportHistory');
            Route::post('export-delete/{reportId}', 'deleteExportedReport');
        });

        Route::prefix('geofences')->controller(GeofenceController::class)->group(function () {
            Route::get('/', 'index');
            Route::get('/{id}', 'show');
            Route::post('/', 'store');
            Route::post('update', 'update');
            Route::post('delete', 'destroy');
            Route::get('/vehicles/list', 'getVehicles');
            Route::post('/assign-vehicles', 'assignVehicles');
            Route::post('/remove-vehicle', 'removeVehicle');
            Route::post('/vehicles/sync', 'syncVehicleGeofences');
        });
    });



    Route::controller(RouteTrackingController::class)->group(function () {
        Route::post('update-route', 'updateRoute');
        Route::post('complete-route', 'completeRoute');
    });

    Route::controller(DeviceController::class)->group(function () {
        Route::post('log-device-event', 'logDeviceEvent');
        Route::post('log-geofence-event', 'logDeviceGeofenceEvent');
        Route::post('save-command-response', 'saveCommandResponse');
    });
});
