<main class="bg-slate-50 dark:bg-slate-900">
    <div class="grid text-sm md:grid-cols-2 md:h-screen">

        <img class="hidden object-cover object-center w-full h-full md:block"
            src="{{ asset('assets/images/login.jpeg') }}" alt="Login">

        <div class="p-10 md:p-16">
            <div class="flex items-center justify-between gap-3">
                <img class="h-8 dark:hidden" src="{{ asset('assets/images/logo.svg') }}" alt="logo">
                <img class="hidden h-8 dark:block" src="{{ asset('assets/images/logo-light.png') }}" alt="logo">
                <div class="text-slate-600 dark:text-slate-300">
                    <livewire:components.lang-switcher />
                </div>
            </div>

            <img class="object-cover object-center w-full mt-8 md:h-full md:hidden rounded-2xl"
                src="{{ asset('assets/images/login.jpeg') }}" alt="Login">


            <div class="flex flex-col items-center justify-center mt-10 md:mt-5 md:h-full">
                <div class="flex-shrink-0 w-full">
                    <h1 class="text-2xl font-semibold font-poppins text-slate-700 dark:text-slate-100">
                        @lang('translations.reset_password')
                    </h1>

                    @if($success)
                    <div class="p-4 rounded-md bg-green-50">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="w-5 h-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">
                                    {{ __('app.password_reset_success') }}
                                </h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p>{{ __('app.password_reset_success_message') }}</p>
                                </div>
                                <div class="mt-4">
                                    <div class="-mx-2 -my-1.5 flex gap-2">
                                        <button type="button" onclick="window.close()"
                                            class="bg-green-50 px-2 py-1.5 rounded-md text-sm font-medium text-green-800 hover:bg-green-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600">
                                            {{ __('app.return_to_app') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @else
                    <form class="mt-8 space-y-6" wire:submit.prevent="resetPassword">
                        <div class="-space-y-px rounded-md shadow-sm">
                            <div>
                                <label for="password" class="sr-only">{{ __('translations.new_password') }}</label>
                                <input wire:model="password" id="password" name="password" type="password" required
                                    class="relative block w-full px-3 py-2 text-gray-900 placeholder-gray-500 border border-gray-300 rounded-none appearance-none rounded-t-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                                    placeholder="{{ __('translations.new_password') }}">
                                @error('password')
                                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                                @enderror
                            </div>
                            <div>
                                <label for="password_confirmation" class="sr-only">{{
                                    __('translations.confirm_password') }}</label>
                                <input wire:model="password_confirmation" id="password_confirmation"
                                    name="password_confirmation" type="password" required
                                    class="relative block w-full px-3 py-2 text-gray-900 placeholder-gray-500 border border-gray-300 rounded-none appearance-none rounded-b-md focus:outline-none focus:ring-primary focus:border-primary focus:z-10 sm:text-sm"
                                    placeholder="{{ __('translations.confirm_password') }}">
                                @error('password_confirmation')
                                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <div>
                            <button type="submit"
                                class="relative flex justify-center w-full px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md bg-primary group hover:bg-primaryDark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                <span class="absolute inset-y-0 left-0 flex items-center pl-3">
                                    <svg class="w-5 h-5 text-white group-hover:text-gray-100" viewBox="0 0 20 20"
                                        fill="currentColor">
                                        <path fill-rule="evenodd"
                                            d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                                            clip-rule="evenodd" />
                                    </svg>
                                </span>
                                {{ __('translations.reset_password') }}
                            </button>
                        </div>
                    </form>
                    @endif
                </div>

            </div>
        </div>

    </div>
</main>