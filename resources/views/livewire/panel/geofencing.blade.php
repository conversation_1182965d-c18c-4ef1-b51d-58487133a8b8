<div class="relative flex flex-col-reverse h-auto min-h-[80vh] text-sm md:h-[90%] overflow-y-scroll md:flex-row grow">

    <script
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCbWYIwqkYWWpDIEsiA7sjLmyXolx0HExA&libraries=places,drawing">
    </script>

    @can('geofence_view')
        {{-- sidebar --}}
        <div
            class="p-5 bg-white dark:bg-slate-800 shadow-[5px_0px_5px_rgba(0,0,0,0.1)] overflow-y-scroll h-full md:w-[40%] w-full">


            @if ($manageVehicleGeofenes == false)

                @if ($addEditGeofenceMode == false)
                    <h2 class="text-lg font-semibold text-slate-800 dark:text-slate-100">
                        @lang('translations.existing_geofences')
                    </h2>

                    @can('geofence_add')
                        <button wire:click="addEditGeofence"
                            class="flex items-center justify-center w-full gap-2 px-4 py-2 my-3 text-center text-white transition-all duration-300 rounded-md bg-primary hover:bg-primaryDark">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                stroke="currentColor" class="w-5 h-5">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg> @lang('translations.add_new')
                        </button>
                    @endcan

                    <label
                        class="flex items-center mt-3 text-sm border rounded-sm ps-2 border-slate-300 focus-within:border-slate-400 text-slate-700 dark:text-slate-300 dark:border-slate-400">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-5">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                        </svg>
                        <input type="text" wire:model.live.debounce.500ms="search"
                            class="px-2 py-2 bg-transparent border-none outline-none" placeholder="@lang('translations.search')">
                    </label>

                    <div class="flex flex-wrap gap-2 mt-3 text-xs">
                        <button wire:click="setFilter('all')"
                            class="px-3 py-2 text-gray-500 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-300 @if ($filter == 'all') ring-2 ring-red-300 border-primary text-primary @endif">
                            @lang('translations.all')
                        </button>
                        <button wire:click="setFilter('active')"
                            class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-green-500 hover:text-green-500 focus:outline-none focus:ring-2 focus:ring-green-300  @if ($filter == 'moving') border-green-300 ring-2 ring-green-300 @endif">
                            <span class="bg-green-500 rounded-full size-2"></span>
                            <span>@lang('translations.active')</span>
                        </button>

                        <button wire:click="setFilter('inactive')"
                            class="flex items-center px-3 py-2 space-x-2 text-gray-800 border border-gray-300 rounded-lg dark:text-slate-300 hover:border-red-500 hover:text-red-500 focus:outline-none focus:ring-2 focus:ring-red-300  @if ($filter == 'stopped') ring-2 ring-red-300 border-red-300 @endif">
                            <span class="bg-red-500 rounded-full size-2"></span>
                            <span>@lang('translations.inactive')</span>
                        </button>

                    </div>

                    <div class="mt-3 text-gray-500 dark:text-slate-300">
                        @lang('translations.zones') ({{ count($geofences) ?? 0 }})
                    </div>

                    <div class="mt-3 space-y-2">
                        @foreach ($geofences as $geofence)
                            <div wire:key="geofence-{{ $geofence['id'] ?? 0 }}"
                                class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-slate-400">
                                <!-- Left Section -->
                                <div class="flex items-center space-x-4">
                                    <!-- Status Dot -->
                                    <span
                                        class="{{ $geofence['is_active'] ? 'bg-green-500' : 'bg-red-500' }} rounded-full size-2 shrink-0"></span>
                                    <!-- Content -->
                                    <div>
                                        <p class="text-sm font-semibold text-gray-900 dark:text-slate-100">
                                            {{ $geofence['name'] }}</p>
                                        <div class="flex items-center space-x-2 text-xs text-gray-600 dark:text-slate-300">
                                            <span>{{ $geofence['location'] }}</span>
                                        </div>
                                    </div>
                                </div>
                                <!-- Right Section -->
                                <div class="flex items-center shrink-0">
                                    <!-- Edit Button with Tooltip -->
                                    @can('geofence_vehicle_assignment')
                                        <div wire:click="manageGeofenceVehicles({{ $geofence['id'] }})" x-data="{ showTooltip: false }"
                                            class="relative flex-shrink-0">
                                            <button @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/icons/vehicle.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.vehicles')
                                            </div>
                                        </div>
                                    @endcanany


                                    @can('geofence_edit')
                                        <!-- Edit Button with Tooltip -->
                                        <div wire:click="addEditGeofence({{ $geofence['id'] }})" x-data="{ showTooltip: false }"
                                            class="relative flex-shrink-0 ms-1">
                                            <button @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/icons/edit.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.edit')
                                            </div>
                                        </div>
                                    @endcan

                                    @can('geofence_delete')
                                        <!-- Delete Button with Tooltip -->
                                        <div wire:click="deleteRecordConfirmation({{ $geofence['id'] }})"
                                            x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/icons/delete.svg') }}"
                                                    alt="Delete">
                                            </button>
                                            <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.delete')
                                            </div>
                                        </div>
                                    @endcan
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <button wire:click="backToList" class="dark:text-slate-300">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="currentColor" class="size-6">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18" />
                        </svg>

                    </button>

                    <h2 class="mt-4 text-lg font-semibold text-slate-800 dark:text-slate-100">
                        @if (!$geofenceId)
                            @lang('translations.add_geofence')
                        @else
                            @lang('translations.edit_geofence')
                        @endif
                    </h2>


                    <div class="mt-5">
                        <div x-data="{
                            query: '',
                            places: [],
                            debounceTimer: null,
                            debouncedFetchPlaces() {
                                clearTimeout(this.debounceTimer);
                                this.debounceTimer = setTimeout(() => this.fetchPlaces(), 500);
                            },
                            fetchPlaces() {
                                if (this.query.length < 3) {
                                    this.places = [];
                                    return;
                                }

                                const service = new google.maps.places.AutocompleteService();

                                service.getPlacePredictions({
                                    input: this.query,
                                    types: ['geocode'], // Customize as needed
                                }, (predictions, status) => {
                                    if (status !== google.maps.places.PlacesServiceStatus.OK || !predictions) {
                                        console.error('Failed to fetch place predictions:', status);
                                        return;
                                    }

                                    this.places = predictions;
                                });
                            },
                            selectPlace(place) {
                                this.query = place.description;
                                this.places = [];

                                // Fetch place details using Geocoding API
                                const geocoder = new google.maps.Geocoder();
                                geocoder.geocode({
                                    placeId: place.place_id
                                }, (results, status) => {
                                    if (status === google.maps.GeocoderStatus.OK && results[0]) {
                                        const location = results[0].geometry.location;


                                        if (location) {
                                            const lat = location.lat();
                                            const lng = location.lng();

                                            @this.set('location', place?.description);
                                            @this.set('name', place?.structured_formatting?.main_text);

                                            setMapLocation(lat, lng, 15); // Zoom level set to 15

                                        } else {
                                            console.error('Location is null in geocode response.');
                                        }
                                    } else {
                                        console.error('Failed to fetch geocode details:', status);
                                    }
                                });
                            }
                        }">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.location')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="location" id="location"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-900 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.search_for_a_location')" x-model="query" @input="debouncedFetchPlaces">

                                <ul class="absolute z-10 w-full mt-1 overflow-auto bg-white border border-gray-300 rounded-sm dark:bg-slate-700 dark:border-slate-500 max-h-40"
                                    x-show="places.length > 0" @click.outside="places = []">
                                    <template x-for="(place, index) in places" :key="index">
                                        <li @click="selectPlace(place)"
                                            class="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-800 dark:text-slate-200">
                                            <span x-text="place.description"></span>
                                        </li>
                                    </template>
                                </ul>
                            </div>

                            @error('location')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>



                        <div class="mt-4">
                            <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.name')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="name" id="name"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-900 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter') @lang('translations.name')">
                            </div>

                            @error('name')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                    </div>

                    <div class="mt-4">
                        <label class="text-sm text-[#414651] dark:text-slate-200">@lang('translations.notification_settings')</label>
                        <div class="flex flex-col gap-2 mt-2">
                            <label class="flex items-center gap-2 dark:text-slate-300">
                                <input type="checkbox" wire:model="push_notification" id="push-notification"
                                    class="accent-primary">

                                @lang('translations.push_notifications')
                            </label>

                            <label class="flex items-center gap-2 dark:text-slate-300">
                                <input type="checkbox" wire:model="email_notification" id="email-notification"
                                    class="accent-primary">
                                @lang('translations.email_notifications')

                            </label>

                        </div>
                    </div>


                    <div class="flex items-center mt-4">
                        <label class="relative inline-flex items-center cursor-pointer">
                            <input type="checkbox" checked="" wire:model="is_active" id="active"
                                class="sr-only peer">
                            <div
                                class="w-9 h-5 bg-gray-200 hover:bg-gray-300 dark:bg-slate-400 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                            </div>
                        </label>

                        <label for="active"
                            class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-200">
                            @lang('translations.active')
                        </label>
                    </div>

                    <button wire:click="createUpdateGeofence"
                        class="flex items-center justify-center w-full gap-2 px-4 py-2 my-3 text-center text-white transition-all duration-300 rounded-md bg-primary hover:bg-primaryDark">

                        @lang('translations.confirm')

                    </button>
                @endif
            @else
                <button wire:click="backToList" class="dark:text-slate-300">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="size-6">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M10.5 19.5 3 12m0 0 7.5-7.5M3 12h18" />
                    </svg>
                </button>


                <button @click="$dispatch('open-modal', {name:'add-geofence-vehicle-modal'})"
                    class="flex items-center justify-center w-full gap-2 px-4 py-2 my-3 text-center text-white transition-all duration-300 rounded-md bg-primary hover:bg-primaryDark">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="w-5 h-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg> @lang('translations.add_new')
                </button>


                <label
                    class="flex items-center mt-3 text-sm border rounded-sm ps-2 border-slate-300 focus-within:border-slate-400 text-slate-700 dark:text-slate-300 dark:border-slate-400">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="currentColor" class="size-5">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z" />
                    </svg>
                    <input type="text" wire:model.live.debounce.500ms="vehicleSearch"
                        class="px-2 py-2 bg-transparent border-none outline-none" placeholder="@lang('translations.search')">
                </label>


                <div class="mt-3 space-y-2">
                    @forelse ($vehicles as $vehicle)
                        <div wire:key="vehicle-{{ $vehicle->id }}"
                            class="flex items-center justify-between pb-4 border-b border-gray-200 dark:border-slate-400">
                            <!-- Left Section -->
                            <div class="flex items-center space-x-4">
                                <!-- Content -->
                                <span class="shrink-0">
                                    <img class="size-6" src="{{ asset('assets/images/icons/' . $vehicle->vehicle?->type . '.svg') }}"
                                        alt="Car">

                                </span>
                                <div>
                                    <p class="text-sm font-semibold text-gray-900 dark:text-slate-100">
                                        {{ $vehicle->vehicle?->license_plate }}</p>
                                    <div class="flex items-center gap-2 mt-1 text-xs text-gray-600 dark:text-slate-300">
                                        <span class="inline-flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="currentColor"
                                                viewBox="0 0 24 24">
                                                <path
                                                    d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-3.31 0-6 2.69-6 6h12c0-3.31-2.69-6-6-6z">
                                                </path>
                                            </svg>
                                        </span>
                                        <span>{{ $vehicle->vehicle?->driver?->name }}</span>
                                    </div>
                                    @if($vehicle->zone_number)
                                        <div class="flex items-center gap-2 mt-1 text-xs">
                                            <span class="px-2 py-1 text-white bg-blue-500 rounded-full text-[10px]">@lang('translations.zone') {{ $vehicle->zone_number }}</span>
                                            @if($vehicle->vehicle_imei)
                                                <span class="text-gray-500 dark:text-slate-400">IMEI: {{ $vehicle->vehicle_imei }}</span>
                                            @endif
                                        </div>
                                    @endif
                                </div>
                            </div>
                            <!-- Right Section -->
                            <div class="flex items-center space-x-1">
                                <!-- Sync Button with Tooltip -->
                                @if($vehicle->vehicle?->imei)
                                    <div wire:click="syncVehicleGeofences({{ $vehicle->vehicle_id }}, {{ $vehicle->id }})" x-data="{ showTooltip: false }"
                                        class="relative flex-shrink-0">
                                        <button @mouseenter="showTooltip = true" @mouseleave="showTooltip = false"
                                            class="p-1 text-blue-600 transition-colors duration-200 rounded hover:bg-blue-100 dark:text-blue-400 dark:hover:bg-blue-900">
                                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-4">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0 3.181 3.183a8.25 8.25 0 0 0 13.803-3.7M4.031 9.865a8.25 8.25 0 0 1 13.803-3.7l3.181 3.182m0-4.991v4.99" />
                                            </svg>
                                        </button>
                                        <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                            x-transition:enter-start="opacity-0 transform scale-95"
                                            x-transition:enter-end="opacity-100 transform scale-100"
                                            x-transition:leave="transition ease-in duration-150"
                                            x-transition:leave-start="opacity-100 transform scale-100"
                                            x-transition:leave-end="opacity-0 transform scale-95"
                                            class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                            style="display: none;">
                                            @lang('translations.sync_device')
                                        </div>
                                    </div>
                                @endif

                                <!-- Edit Button with Tooltip -->
                                <div wire:click="editAssignedVehicleGeofence({{ $vehicle->id }})" x-data="{ showTooltip: false }"
                                    class="relative flex-shrink-0">
                                    <button @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                        <img class="size-5" src="{{ asset('assets/images/icons/edit.svg') }}"
                                            alt="Edit">
                                    </button>
                                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 transform scale-95"
                                        x-transition:enter-end="opacity-100 transform scale-100"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 transform scale-100"
                                        x-transition:leave-end="opacity-0 transform scale-95"
                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                        style="display: none;">
                                        @lang('translations.edit')
                                    </div>
                                </div>

                                <!-- Delete Button with Tooltip -->
                                <div wire:click="removeVehicleFromGeofence({{ $vehicle->id }})" x-data="{ showTooltip: false }"
                                    class="relative flex-shrink-0">
                                    <button @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                        <img class="size-5" src="{{ asset('assets/images/icons/delete.svg') }}"
                                            alt="Delete">
                                    </button>
                                    <div x-show="showTooltip" x-transition:enter="transition ease-out duration-200"
                                        x-transition:enter-start="opacity-0 transform scale-95"
                                        x-transition:enter-end="opacity-100 transform scale-100"
                                        x-transition:leave="transition ease-in duration-150"
                                        x-transition:leave-start="opacity-100 transform scale-100"
                                        x-transition:leave-end="opacity-0 transform scale-95"
                                        class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                        style="display: none;">
                                        @lang('translations.delete')
                                    </div>
                                </div>
                            </div>
                        </div>

                    @empty
                        <p class="mt-3 dark:text-slate-300">@lang('translations.no_vehicle_found')</p>
                    @endforelse
                </div>

            @endif

        </div>



        {{-- map --}}
        <div wire:ignore class="w-full overflow-hidden">


            <style>
                #map {
                    height: 100%;
                    width: 100%;
                    min-height: 400px;
                    z-index: 1 !important;
                    /* Set the height of the map */
                }
            </style>

            <div id="map"></div>

            <script>
                var map;
                var geofences = []; // Store geofence data
                var drawingManager;
                var currentGeofence = null;
                var drawnGeofences = []; // Store drawn geofences on the map

                let controlDiv; // Declare controlDiv in a wider scope to access later




                // Initialize the map
                function initializeMap() {
                    // Check if the HTML tag has the 'dark' class
                    const isDarkMode = document.documentElement.classList.contains('dark');

                    map = new google.maps.Map(document.getElementById('map'), {
                        center: {
                            lat: 41.9028,
                            lng: 12.4964
                        }, // Coordinates for Rome
                        zoom: 5,
                        mapTypeId: isDarkMode ? google.maps.MapTypeId.HYBRID : google.maps.MapTypeId
                            .ROADMAP, // Use hybrid in dark mode for satellite with labels
                    });


                    // Initialize drawing manager
                    drawingManager = new google.maps.drawing.DrawingManager({
                        drawingMode: google.maps.drawing.OverlayType.NONE,
                        drawingControl: true,
                        drawingControlOptions: {
                            position: google.maps.ControlPosition.TOP_CENTER,
                            drawingModes: ['polygon', 'circle', 'rectangle']
                        },
                        polygonOptions: {
                            fillColor: '#FF0000',
                            fillOpacity: 0.35,
                            strokeWeight: 2,
                            clickable: true,
                            editable: true,
                            zIndex: 1
                        },
                        circleOptions: {
                            fillColor: '#FF0000',
                            fillOpacity: 0.35,
                            strokeWeight: 2,
                            clickable: true,
                            editable: true,
                            zIndex: 1
                        },
                        rectangleOptions: {
                            fillColor: '#FF0000',
                            fillOpacity: 0.35,
                            strokeWeight: 2,
                            clickable: true,
                            editable: true,
                            zIndex: 1
                        }
                    });
                    drawingManager.setMap(map);

                    // Event listener for when a shape is created
                    google.maps.event.addListener(drawingManager, 'overlaycomplete', function(event) {
                        var newGeofenceData = {};
                        var shape = event.overlay;

                        if (shape instanceof google.maps.Polygon) {
                            newGeofenceData = {
                                type: 'polygon',
                                geofence: shape.getPath().getArray(),
                                editable: true
                            };
                        } else if (shape instanceof google.maps.Circle) {
                            newGeofenceData = {
                                type: 'circle',
                                geofence: shape.getCenter(),
                                radius: shape.getRadius(),
                                editable: true
                            };
                        } else if (shape instanceof google.maps.Rectangle) {
                            newGeofenceData = {
                                type: 'rectangle',
                                geofence: shape.getBounds(),
                                editable: true
                            };
                        }

                        // Save the geofence data and update UI or Livewire
                        geofences.push(newGeofenceData);

                        // Example of sending data to Livewire or similar
                        @this.set('geofence', newGeofenceData);
                        drawnGeofences.push(shape);
                    });
                }

                // Function to update geofences on the map
                function updateGeofencesOnMap(geofencesData, editable = false) {
                    // Clear existing geofences
                    drawnGeofences.forEach(function(geofence) {
                        geofence.setMap(null);
                    });
                    drawnGeofences = [];

                    geofencesData.forEach(function(geofenceData) {
                        let geofence;

                        // Function to show an info modal/popover
                        function showGeofenceInfo(geofenceData) {
                            const name = geofenceData?.name || 'Unknown Name';
                            const locatonName = geofenceData?.location || 'Unknown Name';
                            const location = JSON.stringify(geofenceData?.geofence_data?.geofence || {});

                            const content = locatonName;

                            const infoWindow = new google.maps.InfoWindow({
                                content: content,
                            });

                            // Center and zoom the map on the geofence
                            if (geofenceData?.geofence_data?.type === 'circle') {
                                map.setCenter(geofenceData?.geofence_data?.geofence);
                                map.setZoom(10); // Adjust zoom level as needed
                            } else if (geofenceData?.geofence_data?.type === 'polygon' || geofenceData?.geofence_data
                                ?.type === 'rectangle') {
                                const bounds = new google.maps.LatLngBounds();
                                if (geofenceData?.geofence_data?.type === 'polygon') {
                                    geofenceData?.geofence_data?.geofence.forEach(function(latLng) {
                                        bounds.extend(latLng);
                                    });
                                } else {
                                    bounds.union(geofenceData?.geofence_data?.geofence);
                                }
                                map.fitBounds(bounds);
                            }

                            // Attach the info window to the geofence
                            infoWindow.setPosition(geofenceData?.geofence_data?.geofence);
                            infoWindow.open(map);
                        }

                        // Handle circle geofences
                        if (geofenceData?.geofence_data?.type === 'circle') {
                            geofence = new google.maps.Circle({
                                center: geofenceData?.geofence_data?.geofence,
                                radius: geofenceData?.geofence_data?.radius,
                                fillColor: geofenceData.is_active ? 'green' : 'red',
                                fillOpacity: 0.35,
                                strokeColor: geofenceData?.is_active ? 'green' : 'red',
                                strokeWeight: 2,
                                editable: editable,
                                clickable: true,
                            });

                            // Add click event listener for circle
                            google.maps.event.addListener(geofence, 'click', function() {
                                showGeofenceInfo(geofenceData);
                            });

                            // Add event listeners for editing if editable
                            if (editable) {
                                google.maps.event.addListener(geofence, 'center_changed', function() {
                                    updateGeofenceInLivewire(geofence);
                                });
                                google.maps.event.addListener(geofence, 'radius_changed', function() {
                                    updateGeofenceInLivewire(geofence);
                                });
                            }
                        }

                        // Handle polygon geofences
                        else if (geofenceData?.geofence_data?.type === 'polygon') {
                            geofence = new google.maps.Polygon({
                                paths: geofenceData?.geofence_data?.geofence,
                                fillColor: geofenceData.is_active ? 'green' : 'red',
                                fillOpacity: 0.35,
                                strokeColor: geofenceData?.is_active ? 'green' : 'red',
                                strokeWeight: 2,
                                editable: editable,
                                clickable: true,
                            });

                            // Add click event listener for polygon
                            google.maps.event.addListener(geofence, 'click', function() {
                                showGeofenceInfo(geofenceData);
                            });

                            // Add event listeners for editing if editable
                            if (editable) {
                                const path = geofence.getPath();
                                google.maps.event.addListener(path, 'set_at', function() {
                                    updateGeofenceInLivewire(geofence);
                                });
                                google.maps.event.addListener(path, 'insert_at', function() {
                                    updateGeofenceInLivewire(geofence);
                                });
                                google.maps.event.addListener(path, 'remove_at', function() {
                                    updateGeofenceInLivewire(geofence);
                                });
                            }
                        }

                        // Handle rectangle geofences
                        else if (geofenceData?.geofence_data?.type === 'rectangle') {
                            geofence = new google.maps.Rectangle({
                                bounds: geofenceData?.geofence_data?.geofence,
                                fillColor: geofenceData.is_active ? 'green' : 'red',
                                fillOpacity: 0.35,
                                strokeColor: geofenceData?.is_active ? 'green' : 'red',
                                strokeWeight: 2,
                                editable: editable,
                                clickable: true,
                            });

                            // Add click event listener for rectangle
                            google.maps.event.addListener(geofence, 'click', function() {
                                showGeofenceInfo(geofenceData);
                            });

                            // Add event listener for editing if editable
                            if (editable) {
                                google.maps.event.addListener(geofence, 'bounds_changed', function() {
                                    updateGeofenceInLivewire(geofence);
                                });
                            }
                        }

                        // Add geofence to the map and keep track of it
                        if (geofence) {
                            geofence.setMap(map);
                            drawnGeofences.push(geofence);

                            // If in edit mode, set this as the current geofence
                            if (editable) {
                                currentGeofence = geofence;
                            }
                        }
                    });
                }


                // Helper function to calculate zoom level for a circle
                function getCircleZoomLevel(radius) {
                    const scale = radius / 500; // Adjust this value for zoom sensitivity
                    return Math.floor(15 - Math.log(scale) / Math.LN2); // Approximate zoom level
                }


                // Function to update geofence in Livewire after editing
                function updateGeofenceInLivewire(geofence) {
                    var geofenceData = {};

                    if (geofence instanceof google.maps.Polygon) {
                        // Convert the path to an array of lat/lng objects
                        const pathArray = [];
                        const path = geofence.getPath();
                        for (let i = 0; i < path.getLength(); i++) {
                            const point = path.getAt(i);
                            pathArray.push({
                                lat: point.lat(),
                                lng: point.lng()
                            });
                        }

                        geofenceData = {
                            type: 'polygon',
                            geofence: pathArray,
                            editable: true
                        };
                    } else if (geofence instanceof google.maps.Circle) {
                        const center = geofence.getCenter();
                        geofenceData = {
                            type: 'circle',
                            geofence: {
                                lat: center.lat(),
                                lng: center.lng()
                            },
                            radius: geofence.getRadius(),
                            editable: true
                        };
                    } else if (geofence instanceof google.maps.Rectangle) {
                        const bounds = geofence.getBounds();
                        geofenceData = {
                            type: 'rectangle',
                            geofence: {
                                north: bounds.getNorthEast().lat(),
                                east: bounds.getNorthEast().lng(),
                                south: bounds.getSouthWest().lat(),
                                west: bounds.getSouthWest().lng()
                            },
                            editable: true
                        };
                    }

                    // Log the data being sent to Livewire for debugging
                    console.log('Updating geofence data:', geofenceData);

                    // Log the data being sent to Livewire
                    console.log('Sending updated geofence data to Livewire:', geofenceData);

                    // Send updated geofence data to Livewire
                    @this.set('geofence', geofenceData);
                }


                // Function to clear geofence controls and drawings
                // Function to clear geofence controls and drawings
                function clearGeofenceControls() {
                    // Check and remove the custom control panel
                    const controlsArray = map.controls[google.maps.ControlPosition.TOP_CENTER];
                    for (let i = 0; i < controlsArray.length; i++) {
                        const controlElement = controlsArray.getAt(i);
                        if (controlElement.id === 'customControlPanel') {
                            controlsArray.removeAt(i); // Remove the control panel
                            break;
                        }
                    }

                    // Disable drawing control
                    drawingManager.setOptions({
                        drawingControl: false,
                        drawingMode: null, // Clear the drawable cursor
                    });

                    // Remove all drawn geofences from the map
                    drawnGeofences.forEach(function(geofence) {
                        geofence.setMap(null);
                    });
                    drawnGeofences = [];

                    // Clear the geofences array
                    geofences = [];
                }


                // Function to enable geofence drawing
                function enableGeofence() {
                    // Clear existing geofences
                    clearGeofenceControls();

                    // Custom control panel
                    const controlPanel = document.createElement('div');
                    controlPanel.id = 'customControlPanel'; // Assign a unique ID
                    controlPanel.style.background = '#fff';
                    controlPanel.style.border = '1px solid #ccc';
                    controlPanel.style.padding = '10px';
                    controlPanel.style.margin = '10px';
                    controlPanel.style.borderRadius = '5px';
                    controlPanel.style.boxShadow = '0 2px 6px rgba(0,0,0,0.3)';
                    controlPanel.innerHTML = `
        <button id="drawPolygon" style="margin-right: 5px;">
            <img class="p-1 transition-all duration-200 border size-8 hover:bg-gray-100" src="{{ asset('assets/images/icons/polygon.svg') }}"/>
        </button>
        <button id="drawCircle" style="margin-right: 5px;">
            <img class="p-1 transition-all duration-200 border size-8 hover:bg-gray-100" src="{{ asset('assets/images/icons/circle.svg') }}"/>
        </button>
        <button id="drawRectangle" style="margin-right: 5px;">
            <img class="p-1 transition-all duration-200 border size-8 hover:bg-gray-100" src="{{ asset('assets/images/icons/rectangle.svg') }}"/>
        </button>
        <button id="clearGeofences">
            <img class="p-1 transition-all duration-200 border size-8 hover:bg-gray-100" src="{{ asset('assets/images/icons/delete-icon.svg') }}"/>
        </button>
    `;

                    // Append control panel to the map
                    map.controls[google.maps.ControlPosition.TOP_CENTER].push(controlPanel);


                    // Enable drawing controls
                    const drawPolygonButton = document.getElementById('drawPolygon');
                    const drawCircleButton = document.getElementById('drawCircle');
                    const drawRectangleButton = document.getElementById('drawRectangle');
                    const clearGeofencesButton = document.getElementById('clearGeofences');

                    // Activate polygon drawing
                    drawPolygonButton.addEventListener('click', () => {
                        drawingManager.setDrawingMode(google.maps.drawing.OverlayType.POLYGON);
                    });

                    // Activate circle drawing
                    drawCircleButton.addEventListener('click', () => {
                        drawingManager.setDrawingMode(google.maps.drawing.OverlayType.CIRCLE);
                    });

                    // Activate rectangle drawing
                    drawRectangleButton.addEventListener('click', () => {
                        drawingManager.setDrawingMode(google.maps.drawing.OverlayType.RECTANGLE);
                    });

                    // Clear all geofences
                    clearGeofencesButton.addEventListener('click', () => {
                        drawnGeofences.forEach(geofence => geofence.setMap(null));
                        drawnGeofences = [];
                        geofences = [];
                        @this.set('geofence', []);
                    });


                    // Handle drawing events
                    google.maps.event.addListener(drawingManager, 'overlaycomplete', function(event) {
                        const shape = event.overlay;
                        let newGeofenceData = {};
                        if (shape instanceof google.maps.Polygon) {
                            newGeofenceData = {
                                type: 'polygon',
                                geofence: shape.getPath().getArray(),
                                editable: true
                            };
                        } else if (shape instanceof google.maps.Circle) {
                            newGeofenceData = {
                                type: 'circle',
                                geofence: shape.getCenter(),
                                radius: shape.getRadius(),
                                editable: true
                            };
                        } else if (shape instanceof google.maps.Rectangle) {
                            newGeofenceData = {
                                type: 'rectangle',
                                geofence: shape.getBounds(),
                                editable: true
                            };
                        }

                        // Enable editing for the drawn shape
                        shape.setEditable(true);


                        // Store the geofence
                        geofences.push(newGeofenceData);
                        @this.set('geofence', newGeofenceData);
                        drawnGeofences.push(shape);

                        // Event listeners for editing shapes
                        if (shape instanceof google.maps.Circle) {
                            google.maps.event.addListener(shape, 'center_changed', () => updateGeofenceInLivewire(shape));
                            google.maps.event.addListener(shape, 'radius_changed', () => updateGeofenceInLivewire(shape));
                        } else if (shape instanceof google.maps.Polygon) {
                            // For polygons, we need to listen to path changes
                            const path = shape.getPath();
                            google.maps.event.addListener(path, 'set_at', () => updateGeofenceInLivewire(shape));
                            google.maps.event.addListener(path, 'insert_at', () => updateGeofenceInLivewire(shape));
                            google.maps.event.addListener(path, 'remove_at', () => updateGeofenceInLivewire(shape));
                        } else if (shape instanceof google.maps.Rectangle) {
                            // For rectangles, listen to bounds changes
                            google.maps.event.addListener(shape, 'bounds_changed', () => updateGeofenceInLivewire(shape));
                        }
                    });
                }


                function setMapLocation(lat, lng, zoomLevel = 15) {
                    var newCenter = new google.maps.LatLng(lat, lng);
                    map.setCenter(newCenter);
                    map.setZoom(zoomLevel);
                }

                // Function to center map on a geofence with appropriate zoom
                function centerMapOnGeofence(geofenceData) {
                    if (!geofenceData || !geofenceData.type) return;

                    if (geofenceData.type === 'circle') {
                        // For circle, center on the circle center and zoom based on radius
                        const center = geofenceData.geofence;
                        const radius = geofenceData.radius;
                        map.setCenter(center);

                        // Calculate appropriate zoom level based on radius
                        const zoomLevel = getCircleZoomLevel(radius);
                        map.setZoom(zoomLevel);
                    }
                    else if (geofenceData.type === 'polygon') {
                        // For polygon, create bounds from all points and fit map to those bounds
                        const bounds = new google.maps.LatLngBounds();
                        geofenceData.geofence.forEach(point => {
                            bounds.extend(point);
                        });
                        map.fitBounds(bounds);

                        // Add a slight padding to ensure the entire polygon is visible
                        map.setZoom(map.getZoom() - 0.5);
                    }
                    else if (geofenceData.type === 'rectangle') {
                        // For rectangle, fit map to the rectangle bounds
                        map.fitBounds(geofenceData.geofence);

                        // Add a slight padding
                        map.setZoom(map.getZoom() - 0.5);
                    }
                }

                // Initialize map on page load
                window.onload = initializeMap;
            </script>



            @script
                <script>
                    $wire.on('enable-geofence-mode', () => {
                        setTimeout(() => {
                            enableGeofence();
                        }, 1000);

                    });

                    $wire.on('disable-geofence-mode', () => {
                        setTimeout(() => {
                            clearGeofenceControls();
                        }, 1000);
                    });

                    $wire.on('update-geofences-on-map', (data) => {
                        setTimeout(() => {
                            clearGeofenceControls();
                            updateGeofencesOnMap(data.geofences);
                        }, 1000);
                    });
                    $wire.on('update-geofences-on-map-edit', (data) => {
                        setTimeout(() => {
                            enableGeofence();
                            updateGeofencesOnMap(data.geofences, true);

                            // Center map on the geofence with appropriate zoom
                            if (data.geofences && data.geofences.length > 0) {
                                const geofenceData = data.geofences[0].geofence_data;
                                if (geofenceData) {
                                    centerMapOnGeofence(geofenceData);
                                }
                            }
                        }, 1000);
                    });

                    // Listen for the focus-on-geofence event
                    $wire.on('focus-on-geofence', (data) => {
                        setTimeout(() => {
                            if (data.geofenceData) {
                                centerMapOnGeofence(data.geofenceData);
                            }
                        }, 500);
                    })
                </script>
            @endscript
        </div>
    @endcan


    @can('geofence_delete')
        <x-modal name="delete-record-modal">
            <x-slot:body>

                <div class="flex items-center justify-between">
                    <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                    <button @click="show = false" class="ms-auto">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                            stroke="#808080" class="w-6 h-6 cursor-pointer">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </button>

                </div>

                <div class="mt-4">
                    <h3 class="font-medium dark:text-slate-200">
                        @lang('translations.confirm_delete_record')
                    </h3>

                    <p class="text-sm mt-2 text-[#535862]da rk:text-slate-300 ">
                        @lang('translations.delete_warning')
                    </p>


                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false"
                            class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:text-slate-400 dark:border-slate-600">
                            @lang('translations.cancel')
                        </button>
                        <button wire:click="deleteRecord"
                            class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                            @lang('translations.delete')
                        </button>
                    </div>
                </div>


            </x-slot:body>
        </x-modal>
    @endcan

    <x-modal name="add-geofence-vehicle-modal">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black dark:text-slate-100">
                        @lang('translations.vehicles')
                    </h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">


                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.vehicles')</label>


                            <livewire:components.select-dropdown placeholder="{{ __('translations.select_vehicle') }}"
                                field-name="selectedVehicle" fetch-method="getVehicles" />

                            @error('selectedVehicle')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror

                        </div>

                        <div class="flex items-center col-span-2 mt-2 md:col-span-1">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked="" wire:model="geofence_in_notification"
                                    id="geofence_in_notification" class="sr-only peer">
                                <div
                                    class="w-9 h-5 bg-gray-200 hover:bg-gray-300 dark:bg-slate-400 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                </div>
                            </label>

                            <label for="geofence_in_notification"
                                class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-200">
                                @lang('translations.geofence_in')
                            </label>
                        </div>

                        <div class="flex items-center col-span-2 mt-2 md:col-span-1">
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked="" wire:model="geofence_out_notification"
                                    id="geofence_out_notification" class="sr-only peer">
                                <div
                                    class="w-9 h-5 bg-gray-200 hover:bg-gray-300 dark:bg-slate-400 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                </div>
                            </label>

                            <label for="geofence_out_notification"
                                class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-200">
                                @lang('translations.geofence_out')
                            </label>
                        </div>

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.notifications')</label>
                            <div class="flex flex-col gap-2 mt-2">
                                <label class="flex items-center gap-2 dark:text-slate-300">
                                    <input type="checkbox" wire:model="push_notification" id="push-notification"
                                        class="accent-primary">
                                    @lang('translations.push_notifications')
                                </label>
                                <label class="flex items-center gap-2 dark:text-slate-300">
                                    <input type="checkbox" wire:model="email_notification" id="email-notification"
                                        class="accent-primary">
                                    @lang('translations.email_notifications')
                                </label>
                            </div>
                        </div>

                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] dark:border-slate-600 dark:text-slate-500 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            @lang('translations.cancel')
                        </button>

                        <button wire:click="assignVehicleToGeofence" wire:loading.attr="disabled"
                            wire:target="assignVehicleToGeofence" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-secondary transition-all duration-300">
                            <span wire:loading.remove wire:target="assignVehicleToGeofence"> @lang('translations.save')
                            </span>
                            <div wire:loading wire:target="assignVehicleToGeofence">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>
</div>
