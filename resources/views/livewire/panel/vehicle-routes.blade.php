
<div class="p-4 text-sm">



    <div class="justify-between md:flex">
        <div>
            <h1 class="text-xl font-medium tracking-wide font-poppins text-slate-800 dark:text-slate-100">
                @lang('translations.vehicle_routes')
            </h1>
        </div>
        {{-- @if ($userPermissions['driver_add']) --}}
        <div class="flex justify-end mt-3 h-fit md:mt-0">
            <button wire:click="addDriver"
                class="flex items-center justify-center px-5 py-2.5 text-sm tracking-wide text-white transition-colors duration-200 bg-primary rounded-lg shrink-0 sm:w-auto gap-x-2 hover:bg-secondary ">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                    stroke="currentColor" class="w-5 h-5">
                    <path stroke-linecap="round" stroke-linejoin="round"
                        d="M12 9v6m3-3H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>

                <span>@lang('translations.add_route')</span>
            </button>
        </div>
        {{-- @endif --}}


    </div>


    {{-- @if ($userPermissions['driver_view']) --}}

    <section class="w-full mx-auto mt-4">

        <div class="flex items-center flex-grow gap-4 md:justify-end">

            <div class="w-full max-w-xs">
                <div class="flex items-center w-full space-x-5">
                    <div
                        class="flex w-full p-3 space-x-2 text-sm bg-gray-100 border border-transparent rounded-lg shadow dark:bg-slate-800 focus-within:border-gray-300 dark:focus-within:border-slate-600 dark:text-slate-300">
                        <svg xmlns="http://www.w3.org/2000/svg" class="flex-shrink-0 w-5 h-5 opacity-30" fill="none"
                            viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                        </svg>
                        <input wire:model.live.debounce.500ms="search" class="bg-transparent outline-none"
                            type="search" placeholder="Search" />
                    </div>
                </div>
            </div>

            <div class="w-fit">
                <select wire:model.live="filter_status" class="bg-transparent outline-none dark:text-slate-200">
                    <option selected value="">@lang('translations.status')</option>
                    <option value="completed">@lang('translations.completed')</option>
                    <option value="pending">@lang('translations.pending')</option>
                    <option value="ongoing">@lang('translations.ongoing')</option>
                    <option value="cancelled">@lang('translations.cancelled')</option>
                </select>
            </div>
        </div>



        <div class="w-full mt-4 mb-20 overflow-hidden bg-white rounded-lg shadow dark:bg-slate-800">

            <div class="w-full mb-5 overflow-x-auto">
                <table class="w-full">
                    <thead>
                        <tr
                            class="text-xs font-semibold tracking-wide text-left text-gray-600 uppercase bg-gray-100 dark:bg-slate-900 dark:text-slate-100">
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.start_point')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.end_point')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.date')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.status')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.distance')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.duration')</th>
                            <th class="px-4 py-3 whitespace-nowrap">@lang('translations.created_at')</th>
                            <th class="px-4 py-3 whitespace-nowrap text-end md:min-w-28 min-w-40">@lang('translations.actions')
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-slate-800">
                        @forelse ($driverRoutes as $driverRoute)
                            <tr class="text-gray-700 dark:text-slate-300">
                                <td class="px-4 py-3">
                                    <div>
                                        <p class="text-sm font-semibold text-black dark:text-slate-300">
                                            {{ $driverRoute->start_point ?? '' }}
                                        </p>
                                    </div>
                                </td>
                                <td class="px-4 py-3">
                                    {{ $driverRoute->end_point ?? '' }}

                                </td>
                                <td class="px-4 py-3">

                                    @formatDate($driverRoute->start_date)

                                    {{-- @if ($driverRoute->end_date)
                                        -
                                        @formatDate($driverRoute->end_date)
                                    @endif --}}
                                </td>
                                <td class="px-4 py-3 text-xs">
                                    @if ($driverRoute->status == 'pending')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight text-yellow-700 bg-yellow-100 rounded-full whitespace-nowrap">
                                            @lang('translations.pending') </span>
                                    @elseif ($driverRoute->status == 'completed')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full whitespace-nowrap text-emerald-700 bg-emerald-100">
                                            @lang('translations.completed') </span>
                                    @elseif ($driverRoute->status == 'ongoing')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full whitespace-nowrap text-cyan-700 bg-cyan-100">
                                            @lang('translations.ongoing') </span>
                                    @elseif ($driverRoute->status == 'cancelled')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full whitespace-nowrap text-rose-700 bg-rose-100">
                                            @lang('translations.cancelled') </span>
                                    @endif
                                </td>

                                <td class="px-4 py-3">
                                    {{ $driverRoute->distance ?? '' }} km
                                </td>

                                <td class="px-4 py-3">
                                    @formatDuration($driverRoute->duration ?? 0)
                                </td>

                                <td class="px-4 py-3 text-sm">{{ $driverRoute->created_at->format('d-m-Y H:i') }}
                                </td>

                                <td class="px-4 py-3 text-sm">
                                    <div class="flex items-center justify-end gap-2">



                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="showRecord({{ $driverRoute->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5"
                                                    src="{{ asset('assets/images/icons/eye-icon.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.view')
                                            </div>
                                        </div>



                                        {{-- @if ($userPermissions['driver_edit']) --}}
                                        <!-- Edit Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="editRecord({{ $driverRoute->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5" src="{{ asset('assets/images/icons/edit.svg') }}"
                                                    alt="Edit">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.edit')
                                            </div>
                                        </div>
                                        {{-- @endif --}}

                                        {{-- @if ($userPermissions['driver_delete']) --}}
                                        <!-- Delete Button with Tooltip -->
                                        <div x-data="{ showTooltip: false }" class="relative flex-shrink-0">
                                            <button wire:click="deleteRecordConfirmation({{ $driverRoute->id }})"
                                                @mouseenter="showTooltip = true" @mouseleave="showTooltip = false">
                                                <img class="size-5"
                                                    src="{{ asset('assets/images/icons/delete.svg') }}"
                                                    alt="Delete">
                                            </button>
                                            <div x-show="showTooltip"
                                                x-transition:enter="transition ease-out duration-200"
                                                x-transition:enter-start="opacity-0 transform scale-95"
                                                x-transition:enter-end="opacity-100 transform scale-100"
                                                x-transition:leave="transition ease-in duration-150"
                                                x-transition:leave-start="opacity-100 transform scale-100"
                                                x-transition:leave-end="opacity-0 transform scale-95"
                                                class="absolute right-0 px-3 py-1 mb-2 text-xs text-white bg-gray-800 rounded shadow-lg bottom-full w-max"
                                                style="display: none;">
                                                @lang('translations.delete')
                                            </div>
                                        </div>
                                        {{-- @endif --}}


                                    </div>

                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="10" class="px-4 py-3 text-center dark:text-slate-300">
                                    @lang('translations.no_record_found')
                                </td>
                            </tr>
                        @endforelse

                    </tbody>
                </table>
            </div>

            {{ $driverRoutes->links('livewire.components.pagination') }}

        </div>
    </section>
    {{-- @endif --}}

    {{-- modals --}}

    {{-- @if ($userPermissions['driver_add'] || $userPermissions['driver_edit']) --}}
    <x-modal name="manage-driver-route">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="flex items-center justify-center">
                    <h2 class="text-xl font-medium text-center text-black dark:text-slate-100">
                        @if ($recordId)
                            @lang('translations.edit_route')
                        @else
                            @lang('translations.add_route')
                        @endif
                    </h2>
                </div>


                <div class="mt-4">

                    <div class="grid grid-cols-2 gap-4">

                        <div class="col-span-2 md:col-span-1" x-data="startPointSearch()">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.start_point')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="start_point" id="start_point"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter') @lang('translations.start_point')" x-model="query"
                                    @input="debouncedFetchPlaces">

                                <ul class="absolute z-10 w-full mt-1 overflow-auto bg-white border border-gray-300 rounded-sm dark:bg-slate-700 dark:border-slate-500 max-h-40"
                                    x-show="places.length > 0" @click.outside="places = []">
                                    <template x-for="(place, index) in places" :key="index">
                                        <li @click="selectPlace(place)"
                                            class="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-800 dark:text-slate-200">
                                            <span x-text="place.description"></span>
                                        </li>
                                    </template>
                                </ul>
                            </div>

                            @error('start_point')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <script>
                            function startPointSearch() {
                                return {
                                    query: '',
                                    places: [],
                                    debounceTimer: null,
                                    debouncedFetchPlaces() {
                                        clearTimeout(this.debounceTimer);
                                        this.debounceTimer = setTimeout(() => this.fetchPlaces(), 500);
                                    },
                                    fetchPlaces() {
                                        if (this.query.length < 3) {
                                            this.places = [];
                                            return;
                                        }

                                        const service = new google.maps.places.AutocompleteService();

                                        service.getPlacePredictions({
                                            input: this.query,
                                            types: ['geocode'], // Customize as needed
                                        }, (predictions, status) => {
                                            if (status !== google.maps.places.PlacesServiceStatus.OK || !predictions) {
                                                console.error('Failed to fetch place predictions:', status);
                                                return;
                                            }

                                            this.places = predictions;
                                        });
                                    },
                                    selectPlace(place) {
                                        this.query = place.description;
                                        this.places = [];

                                        // Fetch place details using Geocoding API
                                        const geocoder = new google.maps.Geocoder();
                                        geocoder.geocode({
                                            placeId: place.place_id
                                        }, (results, status) => {
                                            if (status === google.maps.GeocoderStatus.OK && results[0]) {
                                                const location = results[0].geometry.location;


                                                if (location) {
                                                    const lat = location.lat();
                                                    const lng = location.lng();

                                                    @this.set('start_point', place.description);
                                                    @this.set('start_point_lat', lat);
                                                    @this.set('start_point_lng', lng);
                                                } else {
                                                    console.error('Location is null in geocode response.');
                                                }
                                            } else {
                                                console.error('Failed to fetch geocode details:', status);
                                            }
                                        });
                                    }
                                };
                            }
                        </script>

                        <div class="col-span-2 md:col-span-1" x-data="endPointSearch()">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.end_point')</label>

                            <div class="relative mt-2">
                                <input type="text" wire:model="end_point" id="end_point"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter') @lang('translations.end_point')" x-model="query"
                                    @input="debouncedFetchPlaces">

                                <ul class="absolute z-10 w-full mt-1 overflow-auto bg-white border border-gray-300 rounded-sm dark:bg-slate-700 dark:border-slate-500 max-h-40"
                                    x-show="places.length > 0" @click.outside="places = []">
                                    <template x-for="(place, index) in places" :key="index">
                                        <li @click="selectPlace(place)"
                                            class="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-800 dark:text-slate-200">
                                            <span x-text="place.description"></span>
                                        </li>
                                    </template>
                                </ul>
                            </div>

                            @error('end_point')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <script>
                            function endPointSearch() {
                                return {
                                    query: '',
                                    places: [],
                                    debounceTimer: null,
                                    debouncedFetchPlaces() {
                                        clearTimeout(this.debounceTimer);
                                        this.debounceTimer = setTimeout(() => this.fetchPlaces(), 500);
                                    },
                                    fetchPlaces() {
                                        if (this.query.length < 3) {
                                            this.places = [];
                                            return;
                                        }

                                        const service = new google.maps.places.AutocompleteService();

                                        service.getPlacePredictions({
                                            input: this.query,
                                            types: ['geocode'], // Customize as needed
                                        }, (predictions, status) => {
                                            if (status !== google.maps.places.PlacesServiceStatus.OK || !predictions) {
                                                console.error('Failed to fetch place predictions:', status);
                                                return;
                                            }

                                            this.places = predictions;
                                        });
                                    },
                                    selectPlace(place) {
                                        this.query = place.description;
                                        this.places = [];

                                        // Fetch place details using Geocoding API
                                        const geocoder = new google.maps.Geocoder();
                                        geocoder.geocode({
                                            placeId: place.place_id
                                        }, (results, status) => {
                                            if (status === google.maps.GeocoderStatus.OK && results[0]) {
                                                const location = results[0].geometry.location;


                                                if (location) {
                                                    const lat = location.lat();
                                                    const lng = location.lng();

                                                    @this.set('end_point', place.description);
                                                    @this.set('end_point_lat', lat);
                                                    @this.set('end_point_lng', lng);
                                                } else {
                                                    console.error('Location is null in geocode response.');
                                                }
                                            } else {
                                                console.error('Failed to fetch geocode details:', status);
                                            }
                                        });
                                    }
                                };
                            }
                        </script>

                        <div class="grid grid-cols-2 col-span-2 gap-2">
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.date')</label>

                                <div class="relative mt-2">
                                    <input type="date" wire:model="start_date" id="start_date"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                </div>

                                @error('start_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                            {{-- <div class="col-span-3 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.end_date')</label>

                                <div class="relative mt-2">
                                    <input type="date" wire:model="end_date" id="end_date"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                </div>

                                @error('end_date')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div> --}}


                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.departure_time')</label>

                                <div class="relative mt-2">
                                    <input type="time" wire:model="departure_time" id="departure_time"
                                        class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                </div>

                                @error('departure_time')
                                    <div class="mt-2 text-xs text-red-500">
                                        {{ $message }}
                                    </div>
                                @enderror

                            </div>

                        </div>


                        <div class="col-span-2">
                            <!-- Stops -->
                            <div class="mt-4" x-data="{ stops: $wire.entangle('stops') }">
                                <label class="flex items-center gap-2 text-sm">
                                    @lang('translations.stops')
                                    <button type="button" @click="$wire.addStop()" class="p-1">
                                        <img class="size-5" src="{{ asset('assets/images/icons/add.svg') }}"
                                            alt="add">
                                    </button>
                                </label>
                                <div>
                                    <template x-for="(stop, index) in stops" :key="index">
                                        <div class="relative flex items-center mt-2" x-data="stopSearch(index)">
                                            <div
                                                class="dark:bg-slate-800 border-[1.5px] dark:border-slate-600 border-e-0 border-[#D5D7DA] dark:text-slate-300 shadow-sm rounded-lg rounded-e-none text-sm py-2.5 px-4 block w-fit">
                                                <span x-text="index + 1"></span>
                                            </div>
                                            <input type="text" :id="'stop_' + index" x-model="stop.stop_point"
                                                @input="debouncedFetchPlaces"
                                                class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-r-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                                placeholder="@lang('translations.enter_stop')">

                                            <ul class="absolute z-10 w-full mt-1 overflow-auto bg-white border border-gray-300 rounded-sm dark:bg-slate-700 dark:border-slate-500 max-h-40 top-full"
                                                x-show="places.length > 0" @click.outside="places = []">
                                                <template x-for="(place, idx) in places" :key="idx">
                                                    <li @click="selectPlace(place)"
                                                        class="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-slate-800 dark:text-slate-200">
                                                        <span x-text="place.description"></span>
                                                    </li>
                                                </template>
                                            </ul>

                                            <button type="button" @click="$wire.removeStop(index)" class="p-1 ms-2">
                                                <img class="size-5"
                                                    src="{{ asset('assets/images/icons/remove.svg') }}"
                                                    alt="remove">
                                            </button>
                                        </div>
                                    </template>
                                </div>
                                @error('stops')
                                    <span class="text-xs text-red-500">{{ $message }}</span>
                                @enderror
                                @error('stops.*')
                                    <span class="text-xs text-red-500">{{ $message }}</span>
                                @enderror
                            </div>
                        </div>

                        <script>
                            function stopSearch(index) {
                                return {
                                    places: [],
                                    debouncedFetchPlaces: debounce(function() {
                                        this.fetchPlaces();
                                    }, 500),
                                    fetchPlaces() {
                                        const inputElement = document.getElementById(`stop_${index}`);
                                        if (!inputElement || inputElement.value.length < 3) {
                                            this.places = [];
                                            return;
                                        }

                                        const service = new google.maps.places.AutocompleteService();
                                        service.getPlacePredictions({
                                                input: inputElement.value,
                                                types: ['geocode']
                                            },
                                            (predictions, status) => {
                                                if (status !== google.maps.places.PlacesServiceStatus.OK || !predictions) {
                                                    this.places = [];
                                                    return;
                                                }
                                                this.places = predictions;
                                            }
                                        );
                                    },
                                    selectPlace(place) {
                                        this.places = [];
                                        // Get latitude and longitude
                                        const geocoder = new google.maps.Geocoder();
                                        geocoder.geocode({
                                            placeId: place.place_id
                                        }, (results, status) => {
                                            if (status === google.maps.GeocoderStatus.OK && results[0]) {
                                                const location = results[0].geometry.location;
                                                // Update Livewire stops array
                                                @this.set(`stops.${index}.stop_point`, place.description);
                                                @this.set(`stops.${index}.latitude`, location.lat());
                                                @this.set(`stops.${index}.longitude`, location.lng());
                                            }
                                        });
                                    }
                                };
                            }

                            // Debounce function
                            function debounce(func, wait) {
                                let timeout;
                                return function(...args) {
                                    const context = this;
                                    clearTimeout(timeout);
                                    timeout = setTimeout(() => func.apply(context, args), wait);
                                };
                            }
                        </script>

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.remarks')</label>

                            <div class="relative mt-2">
                                <textarea wire:model="remarks" id="remarks"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary"
                                    placeholder="@lang('translations.enter_remarks')"></textarea>
                            </div>

                            @error('remarks')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <div class="col-span-2">
                            <label class="text-sm text-[#414651] dark:text-slate-300">@lang('translations.status')</label>

                            <div class="relative mt-2">
                                <select wire:model="status" id="status"
                                    class="peer py-2.5 px-4 block w-full border-[1.5px] border-[#D5D7DA] shadow-sm rounded-lg text-sm focus:border-primary outline-none transition-all duration-300 dark:text-slate-300 dark:bg-slate-800 dark:border-slate-600 dark:focus:border-primary">
                                    <option value="pending">@lang('translations.pending')</option>
                                    <option value="ongoing">@lang('translations.ongoing')</option>
                                    <option value="completed">@lang('translations.completed')</option>
                                    <option value="cancelled">@lang('translations.cancelled')</option>
                                </select>
                            </div>

                            @error('status')
                                <div class="mt-2 text-xs text-red-500">
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        @if (!$end_date)
                            <div class="flex items-center col-span-2">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" wire:model="is_repeat" id="is_repeat"
                                        class="sr-only peer">
                                    <div
                                        class="w-9 h-5 bg-gray-200 hover:bg-gray-300 peer-focus:outline-0 peer-focus:ring-transparent rounded-full peer transition-all ease-in-out duration-500 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-primary hover:peer-checked:bg-primary">
                                    </div>
                                </label>

                                <label for="is_repeat"
                                    class="ms-2 cursor-pointer select-none text-sm text-[#414651] dark:text-slate-300">
                                    @lang('translations.repeat_daily')
                                </label>
                            </div>
                        @endif
                    </div>

                    <div class="mt-5">
                        <button wire:click="generateRoute"
                            class="flex items-center gap-2 px-4 py-2 text-white transition-all duration-300 rounded-md bg-primary hover:bg-primaryDark">Generate
                            Route</button>

                        <div wire:ignore id="map" class="mt-4" style="width: 100%;"></div>

                    </div>

                    <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                        <button @click="show = false;"
                            class="modal-close text-[#414651] dark:border-slate-600 dark:text-slate-500 p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm">
                            @lang('translations.cancel')
                        </button>

                        <button wire:click="addUpdateDriverRoute" wire:loading.attr="disabled"
                            wire:target="addUpdateDriverRoute" type="button"
                            class="text-white p-2.5 text-center w-full bg-primary rounded-lg shadow-sm hover:bg-secondary transition-all duration-300">
                            <span wire:loading.remove wire:target="addUpdateDriverRoute"> @lang('translations.save')
                            </span>
                            <div wire:loading wire:target="addUpdateDriverRoute">
                                <div class="dot-spinner h-[1.4rem!important] w-[1.4rem!important]">
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                    <div class="dot-spinner__dot"></div>
                                </div>
                            </div>
                        </button>
                    </div>
                </div>

            </div>
        </x-slot:body>
    </x-modal>


    @script
        <script>
            let map;
            let directionsService;
            let allRoutes = []; // Store all route polylines
            let activePolyline = null; // Store the currently selected route
            let allMarkers = []; // Store all markers

            window.addEventListener('load-route', event => {
                allRoutes.forEach(route => route.setMap(null));
                allRoutes = [];
                activePolyline = null;

                // Remove all markers
                allMarkers.forEach(marker => marker.setMap(null));
                allMarkers = [];

                let {
                    start,
                    end,
                    stops
                } = event.detail[0];

                // Convert lat/lng values to numbers
                start.lat = parseFloat(start.lat);
                start.lng = parseFloat(start.lng);
                end.lat = parseFloat(end.lat);
                end.lng = parseFloat(end.lng);

                stops = stops.map(stop => ({
                    location: new google.maps.LatLng(parseFloat(stop.latitude), parseFloat(stop
                        .longitude)),
                    stopover: true
                }));

                document.getElementById('map').style.height = '500px';

                // Initialize Map if not already initialized
                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 6,
                        center: start
                    });

                    directionsService = new google.maps.DirectionsService();
                }


                // Add markers for start, stops, and end points
                let markerIndex = 0;

                function addMarker(position, label) {
                    let marker = new google.maps.Marker({
                        position: position,
                        map: map,
                        label: label,
                    });
                    allMarkers.push(marker);
                }

                addMarker(start, "A"); // Start point marker

                stops.forEach((stop, index) => {
                    addMarker(stop.location, String.fromCharCode(66 + index)); // B, C, D...
                });

                addMarker(end, String.fromCharCode(66 + stops.length)); // Last marker (End point)

                // Request multiple route alternatives
                directionsService.route({
                    origin: new google.maps.LatLng(start.lat, start.lng),
                    destination: new google.maps.LatLng(end.lat, end.lng),
                    waypoints: stops,
                    travelMode: google.maps.TravelMode.DRIVING,
                    provideRouteAlternatives: true
                }, (response, status) => {
                    if (status === google.maps.DirectionsStatus.OK) {
                        response.routes.forEach((route, index) => {
                            // Create a polyline for each route
                            let polyline = new google.maps.Polyline({
                                path: route.overview_path,
                                strokeColor: index === 0 ? "#007bff" : "#808080",
                                strokeOpacity: 0.7,
                                strokeWeight: 5,
                                map: map
                            });

                            allRoutes.push(polyline);

                            if (index === 0) {
                                // Prepare data to save
                                let initalilPoline = {
                                    path: route.overview_path.map(point => ({
                                        lat: point.lat(),
                                        lng: point.lng()
                                    }))
                                };

                                // Send to Livewire for storing in DB
                                @this.call('saveSelectedRoute',
                                    initalilPoline);
                            }

                            // Add click event to select a route
                            google.maps.event.addListener(polyline, 'click', function() {

                                // Remove previous active route
                                if (activePolyline) {
                                    activePolyline.setOptions({
                                        strokeColor: "#808080",
                                        strokeOpacity: 0.7,
                                        strokeWeight: 5
                                    });
                                }

                                // Highlight selected route
                                polyline.setOptions({
                                    strokeColor: "#007bff",
                                    strokeOpacity: 1,
                                    strokeWeight: 6
                                });

                                activePolyline = polyline; // Store selected route

                                // Prepare data to save
                                let selectedRouteData = {
                                    path: route.overview_path.map(point => ({
                                        lat: point.lat(),
                                        lng: point.lng()
                                    }))
                                };

                                // Send to Livewire for storing in DB
                                @this.call('saveSelectedRoute',
                                    selectedRouteData);
                            });

                        });

                    } else {
                        alert("Could not display route. Error: " + status);
                    }
                });
            });

            // Event listener to clear the map and remove routes
            window.addEventListener('remove-existing-routes', () => {
                document.getElementById('map').style.height = '0px';
                allRoutes.forEach(route => route.setMap(null));
                allRoutes = [];
                activePolyline = null;

                // Remove all markers
                allMarkers.forEach(marker => marker.setMap(null));
                allMarkers = [];
            });

            window.addEventListener('show-saved-route', event => {
                allRoutes.forEach(route => route.setMap(null));
                allRoutes = [];
                activePolyline = null;

                // Remove all markers
                allMarkers.forEach(marker => marker.setMap(null));
                allMarkers = [];

                map = null

                let {
                    start,
                    end,
                    stops,
                    path
                } = event.detail[0];

                start.lat = parseFloat(start.lat);
                start.lng = parseFloat(start.lng);
                end.lat = parseFloat(end.lat);
                end.lng = parseFloat(end.lng);

                stops = stops.map(stop => ({
                    location: new google.maps.LatLng(parseFloat(stop.latitude), parseFloat(stop
                        .longitude)),
                    stopover: true
                }));


                document.getElementById('map').style.height = '500px';

                // Initialize map if not already created
                if (!map) {
                    map = new google.maps.Map(document.getElementById('map'), {
                        zoom: 8,
                        center: start
                    });
                    directionsService = new google.maps.DirectionsService();

                }


                // Clear previous routes
                allRoutes.forEach(route => route.setMap(null));
                allRoutes = [];

                // Draw saved route path
                let savedPolyline = new google.maps.Polyline({
                    path: path,
                    strokeColor: "#007bff",
                    strokeOpacity: 1,
                    strokeWeight: 6,
                    map: map
                });

                allRoutes.push(savedPolyline);

                // Add markers for start, stops, and end
                function addMarker(position, label) {
                    new google.maps.Marker({
                        position: position,
                        map: map,
                        label: label
                    });
                }

                addMarker(start, "A");

                stops.forEach((stop, index) => {
                    addMarker(stop.location, String.fromCharCode(66 + index)); // B, C, D...
                });

                addMarker(end, String.fromCharCode(66 + stops.length)); // Last marker
            });

            let viewMap;
            window.addEventListener('show-saved-route-view', event => {
                allRoutes.forEach(route => route.setMap(null));
                allRoutes = [];
                activePolyline = null;

                // Remove all markers
                allMarkers.forEach(marker => marker.setMap(null));
                allMarkers = [];

                viewMap = null

                let {
                    start,
                    end,
                    stops,
                    path
                } = event.detail[0];

                start.lat = parseFloat(start.lat);
                start.lng = parseFloat(start.lng);
                end.lat = parseFloat(end.lat);
                end.lng = parseFloat(end.lng);


                stops = stops.map(stop => ({
                    location: new google.maps.LatLng(parseFloat(stop.latitude), parseFloat(stop.longitude)),
                    stopover: true
                }));



                // Initialize map if not already created
                if (!viewMap) {
                    viewMap = new google.maps.Map(document.getElementById('viewMap'), {
                        zoom: 8,
                        center: start
                    });
                    directionsService = new google.maps.DirectionsService();

                }


                // Clear previous routes
                allRoutes.forEach(route => route.setMap(null));
                allRoutes = [];

                // Draw saved route path
                let savedPolylineView = new google.maps.Polyline({
                    path: path,
                    strokeColor: "#007bff",
                    strokeOpacity: 1,
                    strokeWeight: 6,
                    map: viewMap
                });

                allRoutes.push(savedPolylineView);

                // Add markers for start, stops, and end
                function addMarkerToView(position, label) {
                    new google.maps.Marker({
                        position: position,
                        map: viewMap,
                        label: label
                    });
                }

                addMarkerToView(start, "A");

                stops.forEach((stop, index) => {
                    addMarkerToView(stop.location, String.fromCharCode(66 + index)); // B, C, D...
                });

                addMarkerToView(end, String.fromCharCode(66 + stops.length)); // Last marker
            });
        </script>
    @endscript




    {{-- @endif --}}

    {{-- @if ($userPermissions['driver_delete']) --}}
    <x-modal name="delete-record-modal">
        <x-slot:body>

            <div class="flex items-center justify-between">
                <img class="size-10" src="{{ asset('assets/images/icons/delete.svg') }}" alt="delete">

                <button @click="show = false" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>

            </div>

            <div class="mt-4">
                <h3 class="font-medium dark:text-slate-200">
                    @lang('translations.confirm_delete_record')
                </h3>

                <p class="text-sm mt-2 text-[#535862]da rk:text-slate-300 ">
                    @lang('translations.delete_warning')
                </p>


                <div class="flex items-center justify-center gap-5 mt-8 text-sm">
                    <button @click="show = false"
                        class="text-[#414651] p-2.5 text-center w-full border-[1.5px] border-[#D5D7DA] rounded-lg shadow-sm dark:text-slate-400 dark:border-slate-600">
                        @lang('translations.cancel')
                    </button>
                    <button wire:click="deleteRecord"
                        class="text-white p-2.5 text-center w-full bg-red-600 rounded-lg shadow-sm">
                        @lang('translations.delete')
                    </button>
                </div>
            </div>


        </x-slot:body>
    </x-modal>
    {{-- @endif --}}


    {{-- @if ($userPermissions['driver_view']) --}}
    <x-modal name="show-record">
        <x-slot:body>
            <!-- Modal Header -->
            <div class="flex items-center justify-between">
                <h2 class="col-span-2 text-lg font-semibold text-center">
                    @lang('translations.vehicle_details')
                </h2>

                <button @click="show = false;" class="ms-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                        stroke="#808080" class="w-6 h-6 cursor-pointer">
                        <path stroke-linecap="round" stroke-linejoin="round"
                            d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </button>
            </div>

            <div class="w-full">

                <div class="mt-4">

                    <div class="grid gap-4 md:grid-cols-2">
                        @if ($selectedRecord)
                            <!-- Starting Point -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.starting_point')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    {{ $selectedRecord->start_point ?? 'N/A' }}
                                </p>
                            </div>

                            <!-- Ending Point -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.ending_point')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    {{ $selectedRecord->end_point ?? 'N/A' }}
                                </p>
                            </div>

                            <!-- Start Date -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.start_date')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    @formatDate($selectedRecord->start_date)
                                </p>
                            </div>

                            <!-- End Date -->
                            {{-- <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.end_date')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    @if ($selectedRecord->end_date)
                                        @formatDate($selectedRecord->end_date)
                                    @endif
                                </p>
                            </div> --}}

                            <!-- departure_time -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.departure_time')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    {{ $selectedRecord->departure_time ?? 'N/A' }}
                                </p>
                            </div>

                            <!-- Stops -->
                            <div class="col-span-2">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.stops')</label>
                                <div class="mt-2">
                                    @if (!empty($selectedRecord->stops))
                                        @php
                                            $routeStops = $selectedRecord->stops ?? [];
                                        @endphp
                                        @foreach ($routeStops as $index => $stop)
                                            <div class="flex items-start space-x-4">
                                                {{-- <p
                                                    class="flex-shrink-0 w-16 text-sm text-gray-600 dark:text-slate-300">
                                                    09:15 am
                                                </p> --}}
                                                @if (count($routeStops) != $index + 1)
                                                    <div class="flex-shrink-0">
                                                        <div
                                                            class="w-4 h-4 border-2  rounded-full @if ($stop->status == 'pending') border-green-500
                                                            @else
                                                            bg-green-500 @endif">
                                                        </div>
                                                        <div class="w-0.5 h-16 bg-[#E3E3E3] mx-auto">
                                                        </div>
                                                    </div>
                                                @else
                                                    <div class="flex-shrink-0">
                                                        <div class="w-4 h-4 border-2 @if ($stop->status == 'pending') border-green-500
                                                            @else
                                                            bg-green-500 @endif rounded-full">
                                                        </div>
                                                        <div class="w-0.5 h-0 bg-[#E3E3E3] mx-auto">
                                                        </div>
                                                    </div>
                                                @endif
                                                <div>

                                                    <p class="font-semibold text-gray-900 dark:text-slate-100">
                                                        {{ $stop->stop_point ?? '' }}</p>
                                                </div>
                                            </div>
                                        @endforeach
                                    @else
                                        <p class="text-sm text-gray-800 dark:text-slate-300">N/A</p>
                                    @endif
                                </div>
                            </div>

                            <!-- Is Repeat -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.repeat_daily')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    {{ $selectedRecord->is_repeat ? __('translations.yes') : __('translations.no') }}
                                </p>
                            </div>

                            <!-- Status -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.status')</label>
                                <p class="mt-2 text-sm text-gray-800 dark:text-slate-300">
                                    @if ($selectedRecord->status == 'pending')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight text-yellow-700 bg-yellow-100 rounded-full">
                                            @lang('translations.pending') </span>
                                    @elseif ($selectedRecord->status == 'completed')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-emerald-700 bg-emerald-100">
                                            @lang('translations.completed') </span>
                                    @elseif ($selectedRecord->status == 'ongoing')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-cyan-700 bg-cyan-100">
                                            @lang('translations.ongoing') </span>
                                    @elseif ($selectedRecord->status == 'cancelled')
                                        <span
                                            class="px-2 py-1 font-medium leading-tight rounded-full text-rose-700 bg-rose-100">
                                            @lang('translations.cancelled') </span>
                                    @endif
                                </p>
                            </div>

                            <!-- Distance -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.distance')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    {{ $selectedRecord->distance ?? 'N/A' }} km
                                </p>
                            </div>

                            <!-- Duration -->
                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.duration')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    @formatDuration($selectedRecord->duration ?? 0)
                                </p>
                            </div>

                            <!-- Remarks -->
                            <div class="col-span-2">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.remarks')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    {{ $selectedRecord->remarks ?? 'N/A' }}
                                </p>
                            </div>






                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.created_at')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    {{ $selectedRecord->created_at->format('d-m-Y H:i') }}</p>
                            </div>

                            <div class="col-span-2 md:col-span-1">
                                <label class="text-sm text-gray-600 dark:text-slate-200">@lang('translations.updated_at')</label>
                                <p class="text-sm text-gray-800 dark:text-slate-300">
                                    @if ($selectedRecord->updated_at)
                                        {{ $selectedRecord->updated_at->format('d-m-Y H:i') }}
                                    @else
                                        N/A
                                    @endif
                                </p>
                            </div>
                        @else
                            <p>@lang('translations.no_record_found')</p>
                        @endif

                        <div wire:ignore id="viewMap" class="w-full col-span-2 mt-4" style="height: 500px">

                        </div>
                    </div>


                </div>

            </div>
        </x-slot:body>
    </x-modal>
    {{-- @endif --}}

    <script async defer
        src="https://maps.googleapis.com/maps/api/js?key=AIzaSyCbWYIwqkYWWpDIEsiA7sjLmyXolx0HExA&libraries=places"></script>


</div>
