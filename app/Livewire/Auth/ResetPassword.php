<?php

namespace App\Livewire\Auth;

use App\Models\PasswordResetToken;
use App\Models\User;
use Illuminate\Support\Facades\Hash;
use Livewire\Attributes\Layout;
use Livewire\Attributes\Title;
use Livewire\Component;

class ResetPassword extends Component
{
    #[Layout("layouts.auth")]
    #[Title("Reset Password - ControllOne")]
    
    public $token;
    public $email;
    public $password;
    public $password_confirmation;
    public $success = false;

    public function mount()
    {
        $this->token = request()->get('token');
        $this->email = request()->get('email');

        if (!$this->token || !$this->email) {
            abort(404);
        }

        // Check if token is valid and not expired
        $passwordResetToken = PasswordResetToken::where('email', $this->email)
            ->where('token', $this->token)
            ->where('created_at', '>=', now()->subHours(24)) // Token expires in 24 hours
            ->first();

        if (!$passwordResetToken) {
            abort(404, 'Invalid or expired reset token');
        }
    }

    public function resetPassword()
    {
        $this->validate([
            'password' => 'required|min:6|confirmed',
        ]);

        try {
            // Verify token again
            $passwordResetToken = PasswordResetToken::where('email', $this->email)
                ->where('token', $this->token)
                ->where('created_at', '>=', now()->subHours(24))
                ->first();

            if (!$passwordResetToken) {
                $this->dispatch(
                    'notify',
                    variant: 'danger',
                    title: 'Error!',
                    message: __('app.invalid_or_expired_token')
                );
                return;
            }

            // Find user and update password
            $user = User::where('email', $this->email)->first();
            
            if (!$user) {
                $this->dispatch(
                    'notify',
                    variant: 'danger',
                    title: 'Error!',
                    message: __('app.user_not_found')
                );
                return;
            }

            // Update password
            $user->password = Hash::make($this->password);
            $user->save();

            // Delete the used token
            $passwordResetToken->delete();

            // Set success flag
            $this->success = true;

            $this->dispatch(
                'notify',
                variant: 'success',
                title: 'Success!',
                message: __('app.password_reset_success')
            );

        } catch (\Exception $e) {
            $this->dispatch(
                'notify',
                variant: 'danger',
                title: 'Error!',
                message: __('app.error_occurred')
            );
        }
    }

    public function render()
    {
        return view('livewire.auth.reset-password');
    }
}
