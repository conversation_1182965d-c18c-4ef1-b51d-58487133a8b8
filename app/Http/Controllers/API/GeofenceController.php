<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Geofence;
use App\Models\Vehicle;
use App\Models\VehicleGeofence;
use App\Models\VehicleUser;
use App\Helpers\TeltonikaCommandHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;

class GeofenceController extends Controller
{
    /**
     * Get user's geofences with optional filtering
     */
    public function index(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
            'search' => 'nullable|string|max:255',
            'filter' => 'nullable|in:all,active,inactive',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();
            $perPage = $request->get('per_page', 15);
            $search = $request->get('search');
            $filter = $request->get('filter', 'all');

            $query = Geofence::query()->when(!$user->can('all_geofences_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orWhereIn('id', VehicleGeofence::where('vehicle_id', function ($query) use ($user) {
                        $query->select('vehicle_id')->from('vehicle_users')->where('user_id', $user->id);
                    })->pluck('geofence_id'));
            });

            // Apply search filter
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('location', 'like', '%' . $search . '%');
                });
            }

            // Apply active/inactive filter
            if ($filter === 'active') {
                $query->where('is_active', 1);
            } elseif ($filter === 'inactive') {
                $query->where('is_active', 0);
            }

            $geofences = $query->latest()->paginate($perPage);

            $formattedGeofences = collect($geofences->items())->map(function ($geofence) {
                return [
                    'id' => $geofence->id,
                    'name' => $geofence->name,
                    'location' => $geofence->location,
                    'is_active' => (bool) $geofence->is_active,
                    'push_notification' => (bool) $geofence->push_notification,
                    'email_notification' => (bool) $geofence->email_notification,
                    'geofence_data' => json_decode($geofence->geofence_data, true),
                    'assigned_vehicles_count' => $geofence->vehicles()->count(),
                    'created_at' => $geofence->created_at->format('d-m-Y H:i'),
                    'updated_at' => $geofence->updated_at->format('d-m-Y H:i'),
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.geofences_retrieved_successfully'),
                'geofences' => $formattedGeofences,
                'pagination' => [
                    'total' => $geofences->total(),
                    'per_page' => $geofences->perPage(),
                    'current_page' => $geofences->currentPage(),
                    'last_page' => $geofences->lastPage(),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get a specific geofence by ID
     */
    public function show($id)
    {
        try {
            $user = Auth::user();
            
            $geofence = Geofence::when(!$user->can('all_geofences_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id)
                    ->orWhereIn('id', VehicleGeofence::where('vehicle_id', function ($query) use ($user) {
                        $query->select('vehicle_id')->from('vehicle_users')->where('user_id', $user->id);
                    })->pluck('geofence_id'));
            })->find($id);

            if (!$geofence) {
                return jsonResponse(false, [
                    'message' => __('app.geofence_not_found')
                ], 404);
            }

            $assignedVehicles = VehicleGeofence::with('vehicle')
                ->where('geofence_id', $id)
                ->get()
                ->map(function ($assignment) {
                    return [
                        'assignment_id' => $assignment->id,
                        'vehicle_id' => $assignment->vehicle_id,
                        'vehicle_name' => $assignment->vehicle->license_plate ?? 'N/A',
                        'vehicle_imei' => $assignment->vehicle_imei,
                        'zone_number' => $assignment->zone_number,
                        'push_notification' => (bool) $assignment->push_notification,
                        'email_notification' => (bool) $assignment->email_notification,
                        'geofence_in_notification' => (bool) $assignment->geofence_in_notification,
                        'geofence_out_notification' => (bool) $assignment->geofence_out_notification,
                    ];
                });

            return jsonResponse(true, [
                'message' => __('app.geofence_retrieved_successfully'),
                'geofence' => [
                    'id' => $geofence->id,
                    'name' => $geofence->name,
                    'location' => $geofence->location,
                    'is_active' => (bool) $geofence->is_active,
                    'push_notification' => (bool) $geofence->push_notification,
                    'email_notification' => (bool) $geofence->email_notification,
                    'geofence_data' => json_decode($geofence->geofence_data, true),
                    'assigned_vehicles' => $assignedVehicles,
                    'created_at' => $geofence->created_at->format('d-m-Y H:i'),
                    'updated_at' => $geofence->updated_at->format('d-m-Y H:i'),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create a new geofence
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'geofence_data' => 'required|array',
            'push_notification' => 'nullable|boolean',
            'email_notification' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $geofence = Geofence::create([
                'name' => $request->name,
                'location' => $request->location,
                'geofence_data' => json_encode($request->geofence_data),
                'push_notification' => $request->get('push_notification', false),
                'email_notification' => $request->get('email_notification', false),
                'is_active' => $request->get('is_active', true),
                'user_id' => Auth::id(),
            ]);

            return jsonResponse(true, [
                'message' => __('app.geofence_created_successfully'),
                'geofence' => [
                    'id' => $geofence->id,
                    'name' => $geofence->name,
                    'location' => $geofence->location,
                    'is_active' => (bool) $geofence->is_active,
                    'push_notification' => (bool) $geofence->push_notification,
                    'email_notification' => (bool) $geofence->email_notification,
                    'geofence_data' => json_decode($geofence->geofence_data, true),
                ]
            ], 201);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update an existing geofence
     */
    public function update(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
            'name' => 'required|string|max:255',
            'location' => 'nullable|string|max:255',
            'geofence_data' => 'required|array',
            'push_notification' => 'nullable|boolean',
            'email_notification' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();
            
            $geofence = Geofence::when(!$user->can('all_geofences_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->find($request->id);

            if (!$geofence) {
                return jsonResponse(false, [
                    'message' => __('app.geofence_not_found')
                ], 404);
            }

            $geofence->update([
                'name' => $request->name,
                'location' => $request->location,
                'geofence_data' => json_encode($request->geofence_data),
                'push_notification' => $request->get('push_notification', false),
                'email_notification' => $request->get('email_notification', false),
                'is_active' => $request->get('is_active', true),
            ]);

            // Update JSON files for all vehicles assigned to this geofence
            $this->updateAllVehiclesForGeofence($geofence->id);

            return jsonResponse(true, [
                'message' => __('app.geofence_updated_successfully'),
                'geofence' => [
                    'id' => $geofence->id,
                    'name' => $geofence->name,
                    'location' => $geofence->location,
                    'is_active' => (bool) $geofence->is_active,
                    'push_notification' => (bool) $geofence->push_notification,
                    'email_notification' => (bool) $geofence->email_notification,
                    'geofence_data' => json_decode($geofence->geofence_data, true),
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a geofence
     */
    public function destroy(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();

            $geofence = Geofence::when(!$user->can('all_geofences_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->find($request->id);

            if (!$geofence) {
                return jsonResponse(false, [
                    'message' => __('app.geofence_not_found')
                ], 404);
            }

            // Remove all vehicle assignments and update device commands
            $vehicleGeofences = VehicleGeofence::where('geofence_id', $request->id)->get();
            foreach ($vehicleGeofences as $vehicleGeofence) {
                $this->removeVehicleFromGeofence($vehicleGeofence->id);
                $vehicleGeofence->delete();
            }

            $geofence->delete();

            return jsonResponse(true, [
                'message' => __('app.geofence_deleted_successfully')
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get user's vehicles for assignment
     */
    public function getVehicles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'search' => 'nullable|string|max:255',
            'geofence_id' => 'nullable|integer|exists:geofences,id',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();
            $search = $request->get('search');
            $geofenceId = $request->get('geofence_id');

            $query = Vehicle::where('status', 1)
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                });

            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('license_plate', 'like', '%' . $search . '%')
                      ->orWhere('imei', 'like', '%' . $search . '%');
                });
            }

            $vehicles = $query->get()->map(function ($vehicle) use ($geofenceId) {
                $isAssigned = false;
                if ($geofenceId) {
                    $isAssigned = VehicleGeofence::where('vehicle_id', $vehicle->id)
                        ->where('geofence_id', $geofenceId)
                        ->exists();
                }

                return [
                    'id' => $vehicle->id,
                    'license_plate' => $vehicle->license_plate,
                    'imei' => $vehicle->imei,
                    'model' => $vehicle->model,
                    'type' => $vehicle->type,
                    'is_assigned' => $isAssigned,
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.vehicles_retrieved_successfully'),
                'vehicles' => $vehicles
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Assign vehicles to a geofence
     */
    public function assignVehicles(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'geofence_id' => 'required|integer',
            'vehicle_ids' => 'required|array|min:1',
            'vehicle_ids.*' => 'integer|exists:vehicles,id',
            'push_notification' => 'nullable|boolean',
            'email_notification' => 'nullable|boolean',
            'geofence_in_notification' => 'nullable|boolean',
            'geofence_out_notification' => 'nullable|boolean',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();

            $geofence = Geofence::when(!$user->can('all_geofences_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->find($request->geofence_id);

            if (!$geofence) {
                return jsonResponse(false, [
                    'message' => __('app.geofence_not_found')
                ], 404);
            }

            $vehicleIds = $request->vehicle_ids;
            $assignedVehicles = [];
            $errors = [];

            foreach ($vehicleIds as $vehicleId) {
                $vehicle = Vehicle::find($vehicleId);

                if (!$vehicle) {
                    $errors[] = "Vehicle with ID {$vehicleId} not found";
                    continue;
                }

                // Check if vehicle is already assigned to the geofence
                if ($geofence->vehicles->contains($vehicleId)) {
                    $errors[] = "Vehicle {$vehicle->license_plate} is already assigned to this geofence";
                    continue;
                }

                // Check if vehicle has IMEI
                if (!$vehicle->imei) {
                    $errors[] = "Vehicle {$vehicle->license_plate} must have an IMEI for device-based geofencing";
                    continue;
                }

                // Check how many geofences are already assigned to this IMEI
                $existingZones = VehicleGeofence::where('vehicle_imei', $vehicle->imei)
                    ->whereNotNull('zone_number')
                    ->count();

                if ($existingZones >= 5) {
                    $errors[] = "Maximum 5 geofences can be assigned to vehicle {$vehicle->license_plate}";
                    continue;
                }

                // Find next available zone number for this IMEI
                $usedZones = VehicleGeofence::where('vehicle_imei', $vehicle->imei)
                    ->whereNotNull('zone_number')
                    ->pluck('zone_number')
                    ->toArray();

                $nextZone = 1;
                for ($i = 1; $i <= 5; $i++) {
                    if (!in_array($i, $usedZones)) {
                        $nextZone = $i;
                        break;
                    }
                }

                // Create the assignment with zone number
                VehicleGeofence::create([
                    'vehicle_id' => $vehicleId,
                    'geofence_id' => $geofence->id,
                    'vehicle_imei' => $vehicle->imei,
                    'zone_number' => $nextZone,
                    'push_notification' => $request->get('push_notification', false),
                    'email_notification' => $request->get('email_notification', false),
                    'geofence_in_notification' => $request->get('geofence_in_notification', false),
                    'geofence_out_notification' => $request->get('geofence_out_notification', false),
                ]);

                // Generate and queue device command
                $command = TeltonikaCommandHelper::generateGeofenceCommand($nextZone, $geofence);
                TeltonikaCommandHelper::addCommandToQueue($vehicle->imei, $command);

                $this->updateVehicleGeofences($vehicleId);

                $assignedVehicles[] = [
                    'vehicle_id' => $vehicleId,
                    'license_plate' => $vehicle->license_plate,
                    'zone_number' => $nextZone,
                ];
            }

            $message = count($assignedVehicles) > 0
                ? __('app.vehicles_assigned_successfully')
                : __('app.no_vehicles_assigned');

            return jsonResponse(true, [
                'message' => $message,
                'assigned_vehicles' => $assignedVehicles,
                'errors' => $errors
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove a vehicle from a geofence
     */
    public function removeVehicle(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'geofence_id' => 'required|integer',
            'vehicle_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();

            $geofence = Geofence::when(!$user->can('all_geofences_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->find($request->geofence_id);

            if (!$geofence) {
                return jsonResponse(false, [
                    'message' => __('app.geofence_not_found')
                ], 404);
            }

            $vehicleGeofence = VehicleGeofence::where('geofence_id', $request->geofence_id)
                ->where('vehicle_id', $request->vehicle_id)
                ->first();

            if (!$vehicleGeofence) {
                return jsonResponse(false, [
                    'message' => __('app.vehicle_assignment_not_found')
                ], 404);
            }

            $this->removeVehicleFromGeofence($vehicleGeofence->id);
            $vehicleGeofence->delete();

            return jsonResponse(true, [
                'message' => __('app.vehicle_removed_successfully')
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Sync geofence commands for a vehicle
     */
    public function syncVehicleGeofences(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'geofence_id' => 'required|integer',
            'vehicle_id' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = Auth::user();

            $geofence = Geofence::when(!$user->can('all_geofences_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->find($request->geofence_id);

            if (!$geofence) {
                return jsonResponse(false, [
                    'message' => __('app.geofence_not_found')
                ], 404);
            }

            $vehicle = Vehicle::find($request->vehicle_id);

            if (!$vehicle || !$vehicle->imei) {
                return jsonResponse(false, [
                    'message' => __('app.vehicle_or_imei_not_found')
                ], 404);
            }

            // Get geofence assignment for this vehicle
            $assignment = VehicleGeofence::with('geofence')
                ->where('vehicle_id', $request->vehicle_id)
                ->where('geofence_id', $request->geofence_id)
                ->whereNotNull('zone_number')
                ->first();

            if (!$assignment) {
                return jsonResponse(false, [
                    'message' => __('app.geofence_assignment_not_found')
                ], 404);
            }

            // Sync commands using helper
            $success = TeltonikaCommandHelper::syncGeofenceCommands($vehicle->imei, $assignment);

            if ($success) {
                return jsonResponse(true, [
                    'message' => __('app.geofence_commands_synced_successfully')
                ]);
            } else {
                return jsonResponse(false, [
                    'message' => __('app.failed_to_sync_geofence_commands')
                ], 500);
            }
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the geofence JSON files for all vehicles assigned to a specific geofence
     */
    private function updateAllVehiclesForGeofence($geofenceId)
    {
        // Get all vehicles assigned to this geofence
        $vehicleGeofences = VehicleGeofence::where('geofence_id', $geofenceId)->get();

        // Update the JSON file for each vehicle
        foreach ($vehicleGeofences as $vehicleGeofence) {
            $this->updateVehicleGeofences($vehicleGeofence->vehicle_id);
        }
    }

    /**
     * Update the geofence JSON file for a specific vehicle
     */
    private function updateVehicleGeofences($vehicleId)
    {
        // Fetch vehicle with assigned geofences
        $vehicle = Vehicle::with(['geofences' => function ($query) {
            $query->where('is_active', 1); // Fetch only active geofences
        }])->find($vehicleId);

        if (!$vehicle) {
            Log::warning('Vehicle not found when updating geofence JSON', ['vehicle_id' => $vehicleId]);
            return false;
        }

        // Extract IMEI from vehicle
        $imei = $vehicle->imei;

        if (!$imei) {
            Log::warning('Vehicle has no IMEI when updating geofence JSON', ['vehicle_id' => $vehicleId]);
            return false;
        }

        // Prepare geofence data
        $geofenceData = $vehicle->geofences->map(function ($geofence) {
            return [
                'id' => $geofence->id,
                'geofence' => json_decode($geofence->geofence_data, true),
            ];
        })->toArray();

        // Define file path
        $directory = public_path('geofences');
        $filePath = $directory . "/{$imei}.json";

        // Ensure directory exists
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true, true);
        }

        // Save data to JSON file
        File::put($filePath, json_encode($geofenceData, JSON_PRETTY_PRINT));

        return true;
    }

    /**
     * Remove vehicle from geofence and update device commands
     */
    private function removeVehicleFromGeofence($vehicleGeofenceId)
    {
        $vehicleGeofence = VehicleGeofence::find($vehicleGeofenceId);

        if (!$vehicleGeofence) {
            return false;
        }

        $vehicleId = $vehicleGeofence->vehicle_id;
        $zoneNumber = $vehicleGeofence->zone_number;
        $imei = $vehicleGeofence->vehicle_imei;

        // Generate command to clear the zone on device
        if ($imei && $zoneNumber) {
            $command = TeltonikaCommandHelper::generateGeofenceCommand($zoneNumber, null);
            TeltonikaCommandHelper::addCommandToQueue($imei, $command);
        }

        $this->updateVehicleGeofences($vehicleId);

        return true;
    }
}
