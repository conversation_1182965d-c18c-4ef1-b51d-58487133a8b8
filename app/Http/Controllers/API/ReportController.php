<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\VehicleUser;
use App\Models\ExportedReport;
use App\Models\Alarm;
use App\Jobs\ProcessReportExport;
use App\Services\RouteReportService;
use App\Services\FuelReportService;
use App\Services\MultiDayReportService;
use App\Exports\ReportExport;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;

class ReportController extends Controller
{
    /**
     * Get vehicles list for reports with search functionality
     */
    public function getVehiclesList(Request $request)
    {
        try {
            $user = auth()->user();
            $search = $request->get('search', '');

            $query = Vehicle::query()
                ->select('id', 'license_plate', 'model', 'imei')
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                })
                ->when($search, function ($query) use ($search) {
                    $query->where(function ($q) use ($search) {
                        $q->where('license_plate', 'like', "%{$search}%")
                            ->orWhere('model', 'like', "%{$search}%")
                            ->orWhere('imei', 'like', "%{$search}%");
                    });
                })
                ->orderBy('license_plate');

            $vehicles = $query->paginate(10);

            $vehiclesList = $vehicles->getCollection()->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'display_name' => $vehicle->license_plate . ' - ' . $vehicle->model
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.vehicles_retrieved_successfully'),
                'vehicles' => $vehiclesList,
                'pagination' => [
                    'total' => $vehicles->total(),
                    'per_page' => $vehicles->perPage(),
                    'current_page' => $vehicles->currentPage(),
                    'last_page' => $vehicles->lastPage()
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate report based on payload
     */
    public function generateReport(Request $request)
    {
        // Validation rules
        $validator = Validator::make($request->all(), [
            'vehicle_id' => 'required|exists:vehicles,id',
            'report_type' => 'required|in:routes,fuel_consumption,alarms',
            'period_type' => 'required|in:daily,monthly,custom',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = auth()->user();
            $vehicleId = $request->vehicle_id;
            $reportType = $request->report_type;
            $periodType = $request->period_type;
            $startDate = $request->start_date;
            $endDate = $request->end_date;

            // Check vehicle access
            if (!$user->can('all_vehicles_access')) {
                $hasAccess = VehicleUser::where('user_id', $user->id)
                    ->where('vehicle_id', $vehicleId)
                    ->exists();

                if (!$hasAccess) {
                    return jsonResponse(false, [
                        'message' => __('app.access_denied')
                    ], 422);
                }
            }

            // Validate date range doesn't exceed 31 days for custom period
            if ($periodType === 'custom') {
                $start = Carbon::parse($startDate);
                $end = Carbon::parse($endDate);

                if ($end->diffInDays($start) > 31) {
                    return jsonResponse(false, [
                        'message' => __('app.date_range_limited_31_days')
                    ], 422);
                }
            }

            $dateRange = [
                'start' => $startDate,
                'end' => $endDate,
                'type' => $periodType
            ];

            // For daily reports, generate immediately
            if ($periodType === 'daily') {
                $reportData = $this->generateImmediateReport($vehicleId, $reportType, $dateRange);

                return jsonResponse(true, [
                    'message' => __('app.report_generated_successfully'),
                    'redirect' => false,
                    'data' => $reportData
                ]);
            } else {
                // For monthly and custom range reports, queue for background processing
                $this->createQueuedExport($vehicleId, $reportType, $periodType, $dateRange);

                return jsonResponse(true, [
                    'message' => __('app.report_being_processed'),
                    'redirect' => true
                ]);
            }
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download daily report as PDF or CSV
     */
    public function downloadDailyReport(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'vehicle_id' => 'required|exists:vehicles,id',
            'report_type' => 'required|in:routes,fuel_consumption,alarms',
            'date' => 'required|date',
            'format' => 'required|in:pdf,csv'
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $user = auth()->user();
            $vehicleId = $request->vehicle_id;
            $reportType = $request->report_type;
            $date = $request->date;
            $format = $request->format;

            // Check vehicle access
            if (!$user->can('all_vehicles_access')) {
                $hasAccess = VehicleUser::where('user_id', $user->id)
                    ->where('vehicle_id', $vehicleId)
                    ->exists();

                if (!$hasAccess) {
                    return jsonResponse(false, [
                        'message' => __('app.access_denied')
                    ], 422);
                }
            }

            $dateRange = [
                'start' => $date,
                'end' => $date,
                'type' => 'daily'
            ];

            $data = $this->generateImmediateReport($vehicleId, $reportType, $dateRange);
            $fileName = $this->generateFileName($format, $dateRange, $vehicleId, $reportType);

            if ($format === 'csv') {
                return Excel::download(new ReportExport($data, $reportType, $date), $fileName);
            } else {
                $pdf = Pdf::loadView('exports.report', [
                    'data' => $data,
                    'reporting_type' => $reportType,
                    'period' => $date
                ]);
                return response()->streamDownload(fn() => print($pdf->stream()), $fileName);
            }
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get export history with pagination
     */
    public function getExportHistory(Request $request)
    {
        try {
            $user = auth()->user();
            $perPage = $request->get('per_page', 10);
            $page = $request->get('page', 1);

            $query = ExportedReport::forUser($user->id)
                ->with('vehicle:id,license_plate,model')
                ->orderBy('created_at', 'desc');

            $reports = $query->paginate($perPage, ['*'], 'page', $page);

            $formattedReports = $reports->getCollection()->map(function ($report) {
                return [
                    'id' => $report->id,
                    'vehicle' => [
                        'id' => $report->vehicle?->id,
                        'license_plate' => $report->vehicle?->license_plate,
                        'model' => $report->vehicle?->model
                    ],
                    'report_type' => $report->report_type,
                    'period_type' => $report->period_type,
                    'start_date' => $report->start_date->format('d/m/Y'),
                    'end_date' => $report->end_date->format('d/m/Y'),
                    'file_name' => $report->file_name,
                    'file_type' => $report->file_type,
                    'file_size_human' => $report->file_size_human,
                    'status' => $report->status,
                    'download_url' => $report->status === 'completed' ? route('reports.download', $report->id) : null,
                    'preview_url' => $report->status === 'completed' && $report->file_type === 'pdf' ? route('reports.preview', $report->id) : null,
                    'created_at' => $report->created_at->format('d/m/Y H:i'),
                    'processing_duration' => $report->processing_duration,
                    'error_message' => $report->error_message
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.export_history_retrieved'),
                'reports' => $formattedReports,
                'pagination' => [
                    'total' => $reports->total(),
                    'per_page' => $reports->perPage(),
                    'current_page' => $reports->currentPage(),
                    'last_page' => $reports->lastPage(),
                    'from' => $reports->firstItem(),
                    'to' => $reports->lastItem()
                ]
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate immediate report for daily reports
     */
    private function generateImmediateReport($vehicleId, $reportType, $dateRange)
    {
        switch ($reportType) {
            case 'routes':
                return RouteReportService::generateRouteReports($vehicleId, $dateRange['start']);

            case 'fuel_consumption':
                return FuelReportService::generateFuelReports($vehicleId, $dateRange['start']);

            case 'alarms':
                return $this->generateAlarmsReport($vehicleId, $dateRange);

            default:
                throw new \Exception("Unsupported report type: {$reportType}");
        }
    }

    /**
     * Generate alarms report
     */
    private function generateAlarmsReport($vehicleId, $dateRange)
    {
        $user = auth()->user();

        $query = Alarm::query()
            ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('vehicle_id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })
            ->when($vehicleId, function ($query) use ($vehicleId) {
                $query->where('vehicle_id', $vehicleId);
            })
            ->whereBetween('created_at', [$dateRange['start'], $dateRange['end'] . ' 23:59:59'])
            ->with(['vehicle', 'geofence'])
            ->latest()
            ->paginate(10);

        return $query;
    }

    /**
     * Create queued export for large reports
     */
    private function createQueuedExport($vehicleId, $reportType, $periodType, $dateRange)
    {
        $fileName = $this->generateFileName('pdf', $dateRange, $vehicleId, $reportType);

        $exportedReport = ExportedReport::create([
            'user_id' => auth()->id(),
            'vehicle_id' => $vehicleId,
            'report_type' => $reportType,
            'period_type' => $periodType,
            'start_date' => $dateRange['start'],
            'end_date' => $dateRange['end'],
            'file_name' => $fileName,
            'file_type' => 'pdf',
            'status' => 'pending',
        ]);

        // Dispatch the job
        ProcessReportExport::dispatch($exportedReport);

        return $exportedReport;
    }

    /**
     * Generate appropriate filename for export
     */
    private function generateFileName($fileType, $dateRange, $vehicleId, $reportType): string
    {
        $extension = $fileType === 'csv' ? 'xlsx' : $fileType;
        $vehicle = $vehicleId ? "vehicle_{$vehicleId}" : 'all_vehicles';
        $period = $dateRange['start'];

        if ($dateRange['start'] !== $dateRange['end']) {
            $period = $dateRange['start'] . '_to_' . $dateRange['end'];
        }

        $uniqueNumber = mt_rand(100000, 999999);

        return "{$reportType}_{$vehicle}_{$period}_{$uniqueNumber}.{$extension}";
    }

    /**
     * Delete an exported report
     */
    public function deleteExportedReport(Request $request, $reportId)
    {
        try {
            $user = auth()->user();
            $report = ExportedReport::forUser($user->id)->findOrFail($reportId);

            // Delete the file from storage
            $report->deleteFile();

            // Delete the record
            $report->delete();

            return jsonResponse(true, [
                'message' => __('app.report_deleted_successfully')
            ]);
        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
