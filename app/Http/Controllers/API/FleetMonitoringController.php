<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Vehicle;
use App\Models\VehicleUser;
use App\Models\VehicleGeofence;
use App\Models\VehicleRouteAssignment;
use App\Models\Driver;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class FleetMonitoringController extends Controller
{
    private const DB_CACHE_DURATION = 300; // 5 minutes in seconds
    private const HISTORY_CACHE_DURATION = 604800; // 7 days in seconds
    private const HISTORY_CACHE_KEY = 'vehicle_history_app_';
    private const DAILY_ODOMETER_CACHE_KEY = 'previous_day_odometer_';
    private const CACHE_DURATION = 86400 * 7; // 7 days in seconds

    /**
     * Get all vehicles with live tracking data
     * Optimized for mobile app consumption with caching
     */
    public function getVehicles()
    {
        try {
            $user = Auth::user();
            $cacheKey = 'fleet_vehicles_' . $user->id;

            // Check cache first - cache until vehicle record is updated or new created
            $cachedData = Cache::get($cacheKey);
            if ($cachedData) {
                return jsonResponse(true, [
                    'message' => __('app.fleet_vehicles_retrieved'),
                    'vehicles' => $cachedData['vehicles'],
                ]);
            }

            // Load live data
            $liveData = $this->loadLiveData();

            // Get all vehicles without any filters (frontend will handle filtering)
            $vehicles = Vehicle::select(
                'id',
                'imei',
                'model',
                'license_plate',
                'type',
                'icon',
                'mileage',
                'current_odometer_reading',
                'driver_id',
                'status'
            )
                ->with(['driver:id,name,user_id'])
                ->where('status', 1)
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                })
                ->get();

            // Enrich vehicles with live data and group by type
            $enrichedVehicles = [];

            foreach ($vehicles as $vehicle) {
                $enrichedVehicle = $this->enrichVehicleWithLiveData($vehicle, $liveData);
                $enrichedVehicles[] = $enrichedVehicle;
            }

            // Cache the results for 5 minutes (will be invalidated when vehicle is updated/created)
            $cacheData = [
                'vehicles' => $enrichedVehicles,
            ];
            Cache::put($cacheKey, $cacheData, 300);

            return jsonResponse(true, [
                'message' => __('app.fleet_vehicles_retrieved'),
                'vehicles' => $enrichedVehicles,
            ]);
        } catch (\Exception $e) {
            Log::error('Fleet vehicles retrieval failed: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }
    /**
     * Get all vehicles with live tracking data
     * Optimized for mobile app consumption with caching
     */
    public function getVehiclesList()
    {
        try {
            $user = Auth::user();
            $cacheKey = 'fleet_vehicles_' . $user->id;

            // Check cache first - cache until vehicle record is updated or new created
            $cachedData = Cache::get($cacheKey);
            if ($cachedData) {
                return jsonResponse(true, [
                    'message' => __('app.fleet_vehicles_retrieved'),
                    'vehicles' => $cachedData['vehicles'],
                ]);
            }

            // Load live data
            $liveData = $this->loadLiveData();

            // Get all vehicles without any filters (frontend will handle filtering)
            $vehicles = Vehicle::select(
                'id',
                'imei',
                'model',
                'license_plate',
                'type',
                'icon',
                'mileage',
                'current_odometer_reading',
                'driver_id',
                'status'
            )
                ->with(['driver:id,name,user_id'])
                ->where('status', 1)
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                })
                ->get();

            // Enrich vehicles with live data and group by type
            $vehiclesByType = [];

            foreach ($vehicles as $vehicle) {
                $enrichedVehicle = $this->enrichVehicleWithLiveData($vehicle, $liveData);

                // Group by vehicle type
                $type = $vehicle->type ?? 'unknown';
                if (!isset($vehiclesByType[$type])) {
                    $vehiclesByType[$type] = [];
                }
                $vehiclesByType[$type][] = $enrichedVehicle;
            }

            // Cache the results for 5 minutes (will be invalidated when vehicle is updated/created)
            $cacheData = [
                'vehicles' => $vehiclesByType,
            ];
            Cache::put($cacheKey, $cacheData, 300);

            return jsonResponse(true, [
                'message' => __('app.fleet_vehicles_retrieved'),
                'vehicles' => $vehiclesByType,
            ]);
        } catch (\Exception $e) {
            Log::error('Fleet vehicles retrieval failed: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }


    /**
     * Load live data from JSON file
     */
    private function loadLiveData()
    {
        $deviceDataPath = public_path('data/live.json');
        try {
            $deviceData = @file_get_contents($deviceDataPath);
            return json_decode($deviceData, true) ?? [];
        } catch (\Exception $e) {
            Log::error('Failed to read live data: ' . $e->getMessage());
            return [];
        }
    }

    /**
     * Enrich vehicle with live tracking data
     */
    private function enrichVehicleWithLiveData($vehicle, $liveData)
    {
        $imei = $vehicle->imei;
        $deviceData = $liveData[$imei] ?? null;

        $enrichedVehicle = $vehicle->toArray();

        if ($deviceData) {
            // GPS coordinates
            $enrichedVehicle['latitude'] = $deviceData['latitude'] ?? null;
            $enrichedVehicle['longitude'] = $deviceData['longitude'] ?? null;

            // Movement and ignition status
            $enrichedVehicle['movement_status'] = ($deviceData['240'] && $deviceData['speed'] > 1) ?? null;
            $enrichedVehicle['ignition_status'] = (isset($deviceData['239']) && $deviceData['239'] == 1) ?? null;

            // Speed and angle
            $enrichedVehicle['speed'] = $deviceData['speed'] ?? 0;
            $enrichedVehicle['angle'] = $deviceData['angle'] ?? 0;

            // Timestamp
            $enrichedVehicle['timestamp'] = $deviceData['last_update'] ?? null;

            // Odometer calculation
            $enrichedVehicle['odometer'] = round(getOdometerValue($deviceData) / 1000);
            if ($vehicle->current_odometer_reading) {
                $enrichedVehicle['odometer'] += $vehicle->current_odometer_reading;
            }

            // Fuel data
            $enrichedVehicle['fuel_used'] = getFuelConsumption($deviceData);
            $enrichedVehicle['average_fuel'] = number_format(
                getAverageFuelValue($enrichedVehicle['fuel_used'], $enrichedVehicle['odometer'], 'l_per_100km'),
                2
            );
            $enrichedVehicle['fuel_level_percentage'] = getFuelLevelPercentage($deviceData);
            $enrichedVehicle['fuel_level'] = getFuelLevelLiters($deviceData);

            // iButton driver detection
            if (isset($deviceData['78']) && $deviceData['78'] != 0 && !preg_match('/^0+$/', $deviceData['78'])) {
                $enrichedVehicle['ibutton'] = $deviceData['78'];
                $currentDriver = Driver::select('id', 'name')->where('ibutton_code', $deviceData['78'])->first();
                $enrichedVehicle['current_driver'] = $currentDriver ? $currentDriver->toArray() : null;
            }
        }

        // Generate pin color based on status
        $enrichedVehicle['pin_color'] = $this->getPinColor(
            $enrichedVehicle['ignition_status'] ?? 0,
            $enrichedVehicle['movement_status'] ?? 0,
            $enrichedVehicle['speed'] ?? 0
        );

        // Generate icon URL
        $vehicleType = $vehicle->type ?? 'car';
        $color = $enrichedVehicle['pin_color'];
        $enrichedVehicle['icon_url'] = asset("assets/images/icons/{$vehicleType}/{$color}.png");

        return $enrichedVehicle;
    }

    /**
     * Get pin color based on vehicle status
     */
    private function getPinColor($ignitionStatus, $movementStatus, $speed = 0)
    {
        if ($movementStatus == 1 && $speed > 0) {
            return "green";
        } else if ($ignitionStatus == 1 && $movementStatus == 0) {
            return "yellow";
        } else {
            if ($movementStatus == 1 && $ignitionStatus == 1) {
                return "green";
            } else {
                return "red";
            }
        }
    }

    /**
     * Get detailed vehicle information
     * Includes GPS data, odometer, fuel, geofences, today's stats, indicators, current route
     */
    public function getVehicleDetails($id)
    {
        try {
            $user = Auth::user();

            // Get vehicle with relationships
            $vehicle = Vehicle::with(['driver:id,name,user_id'])
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                })
                ->findOrFail($id);

            // Load live data
            $liveData = $this->loadLiveData();

            // Enrich vehicle with live data
            $vehicleDetails = $this->enrichVehicleWithLiveData($vehicle, $liveData);

            // Calculate daily distance and fuel
            $dailyStats = $this->calculateDailyStats($vehicleDetails);
            $vehicleDetails['daily_distance'] = $dailyStats['distance'] ?? 0;
            $vehicleDetails['daily_fuel'] = $dailyStats['fuel'] ?? 0;
            $vehicleDetails['address'] = getAddressFromCoordinates($vehicleDetails['latitude'], $vehicleDetails['longitude']);

            // Get assigned geofences
            $geofences = VehicleGeofence::where('vehicle_id', $vehicle->id)
                ->with(['geofence' => function ($query) {
                    $query->where('is_active', 1)
                        ->select('id', 'name', 'location', 'geofence_data');
                }])
                ->get()
                ->map(function ($vg) {
                    return [
                        'id' => $vg->geofence->id,
                        'name' => $vg->geofence->name,
                        'location' => $vg->geofence->location,
                        'geofence_data' => json_decode($vg->geofence->geofence_data, true),
                    ];
                })
                ->toArray();

            // $vehicleDetails['geofences'] = $geofences;

            // Get current route
            $vehicleDetails['current_route'] = $this->getCurrentRoute($vehicle->id);



            // Add indicator data
            $deviceData = $liveData[$vehicle->imei] ?? [];
            $vehicleDetails['indicators'] = [
                'ignition' => isset($deviceData['239']) && $deviceData['239'] == 1,
                'parked' => isset($deviceData['240']) && $deviceData['240'] == 0 && ($vehicleDetails['speed'] ?? 0) == 0,
                'signal' => $deviceData['21'] ?? null
            ];

            // Check geofence status
            $vehicleDetails['indicators']['geofence_status'] = [];
            if (!empty($geofences) && isset($vehicleDetails['latitude']) && isset($vehicleDetails['longitude'])) {
                $point = ['lat' => (float)$vehicleDetails['latitude'], 'lng' => (float)$vehicleDetails['longitude']];

                foreach ($geofences as $geofence) {
                    $isInside = $this->isPointInGeofence($point, $geofence);
                    if ($isInside) {
                        $vehicleDetails['indicators']['geofence_status'] = [
                            'id' => $geofence['id'],
                            'name' => $geofence['name'],
                            'inside' => $isInside
                        ];
                        continue;
                    }
                }
                if (empty($vehicleDetails['indicators']['geofence_status'])) {
                    $vehicleDetails['indicators']['geofence_status'] = [
                        'id' => null,
                        'name' => null,
                        'inside' => false
                    ];
                }
            }

            // Get today's stops from history data
            $todayStops = $this->getTodayStops($vehicle->imei);
            $vehicleDetails['today_stops'] = $todayStops['stops'] ?? [];
            $vehicleDetails['total_stops'] = count($vehicleDetails['today_stops']);
            $vehicleDetails['total_stop_time'] = formatDuration($todayStops['total_stop_time']) ?? 0;

            return jsonResponse(true, [
                'message' => __('app.vehicle_details_retrieved'),
                'vehicle' => $vehicleDetails
            ]);
        } catch (\Exception $e) {
            Log::error('Vehicle details retrieval failed: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('app.error_occurred') . ' ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate daily distance and fuel consumption
     */
    private function calculateDailyStats($vehicle)
    {
        $imei = $vehicle['imei'];
        $currentOdometer = $vehicle['odometer'] ?? 0;
        $currentFuel = $vehicle['fuel_used'] ?? 0;
        $initialReading = $vehicle['current_odometer_reading'] ?? 0;

        // Get previous day's last readings
        $previousDayReadings = $this->getPreviousDayReadings($imei);

        if ($previousDayReadings == null) {
            return [
                'distance' => 0,
                'fuel' => 0
            ];
        }

        // Calculate actual distances (including initial reading only once)
        $currentTotal = $currentOdometer;

        // Convert values to float and handle potential null/invalid values
        $previousDayOdometer = is_numeric($previousDayReadings['odometer']) ? (float)$previousDayReadings['odometer'] : 0;
        $previousDayFuel = is_numeric($previousDayReadings['fuel']) ? (float)$previousDayReadings['fuel'] : 0;
        $initialReading = is_numeric($initialReading) ? (float)$initialReading : 0;

        $previousTotal = $previousDayOdometer + $initialReading;

        return [
            'distance' => $currentTotal > $previousTotal ? $currentTotal - $previousTotal : 0,
            'fuel' => round(max(0, abs($currentFuel - $previousDayFuel)), 2)
        ];
    }

    /**
     * Get previous day readings from cache
     */
    private function getPreviousDayReadings($imei)
    {
        $cacheKey = self::DAILY_ODOMETER_CACHE_KEY . $imei;
        return Cache::get($cacheKey);
    }

    /**
     * Get current route for vehicle
     */
    private function getCurrentRoute($vehicleId, $date = null)
    {
        if ($date) {
            $date = Carbon::createFromFormat('d-m-Y', $date)->format('Y-m-d') ?? now()->format('Y-m-d');
        }

        $vehicleRoute = VehicleRouteAssignment::where('vehicle_id', $vehicleId)
            ->whereDate('start_date', $date ?? now()->toDateString())
            ->with('stops:id,stop_point,vehicle_route_assignment_id')
            ->first(['id', 'start_date', 'end_date', 'start_point', 'end_point', 'departure_time', 'distance', 'duration']);

        if ($vehicleRoute) {
            $routeArray = $vehicleRoute->toArray();
            $routeArray['stops'] = $vehicleRoute->stops?->toArray() ?? [];
            return $routeArray;
        }

        return null;
    }

    /**
     * Check if point is inside geofence
     */
    private function isPointInGeofence($point, $geofence)
    {
        $geofenceData = $geofence['geofence_data'];

        if (!$geofenceData || !isset($geofenceData['type'])) {
            return false;
        }

        if ($geofenceData['type'] == 'circle') {
            return $this->isPointInCircle($point, $geofenceData);
        } elseif ($geofenceData['type'] == 'polygon') {
            return $this->isPointInPolygon($point, $geofenceData['coordinates']);
        }

        return false;
    }

    /**
     * Check if point is inside circle geofence
     */
    private function isPointInCircle($point, $circleData)
    {
        $centerLat = $circleData['geofence']['lat'];
        $centerLng = $circleData['geofence']['lng'];
        $radius = $circleData['radius']; // in meters

        $distance = $this->calculateDistance(
            $point['lat'],
            $point['lng'],
            $centerLat,
            $centerLng
        );

        return $distance <= $radius;
    }

    /**
     * Check if point is inside polygon geofence
     */
    private function isPointInPolygon($point, $polygon)
    {
        $x = $point['lat'];
        $y = $point['lng'];
        $inside = false;

        for ($i = 0, $j = count($polygon) - 1; $i < count($polygon); $j = $i++) {
            $xi = $polygon[$i]['lat'];
            $yi = $polygon[$i]['lng'];
            $xj = $polygon[$j]['lat'];
            $yj = $polygon[$j]['lng'];

            if ((($yi > $y) != ($yj > $y)) && ($x < ($xj - $xi) * ($y - $yi) / ($yj - $yi) + $xi)) {
                $inside = !$inside;
            }
        }

        return $inside;
    }

    /**
     * Calculate distance between two points in meters
     */
    private function calculateDistance($lat1, $lng1, $lat2, $lng2)
    {
        $earthRadius = 6371000; // Earth's radius in meters

        $dLat = deg2rad($lat2 - $lat1);
        $dLng = deg2rad($lng2 - $lng1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
            cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
            sin($dLng / 2) * sin($dLng / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get today's stops from history data
     */
    private function getTodayStops($imei)
    {
        $today = now()->format('d-m-Y');
        return $this->calculateTodayStops($imei, $today);
    }

    /**
     * Calculate today's stops with improved logic and caching
     */
    private function calculateTodayStops($imei, $date = null)
    {
        $today = $date ?? now()->format('d-m-Y');
        $cacheKey = 'today_stops_' . $today . $imei;

        // Try to get from cache first (cache for 5 minutes)
        if ($cachedStops = cache()->get($cacheKey)) {
            return $cachedStops;
        }

        try {
            $filePath = public_path("data/history/{$imei}/{$today}.json");

            if (!file_exists($filePath)) {
                return ['stops' => [], 'total_stop_time' => 0];
            }

            $historyData = json_decode(@file_get_contents($filePath), true);
            if (empty($historyData)) {
                return ['stops' => [], 'total_stop_time' => 0];
            }


            $stops = [];
            $lastTripStop = null;
            $totalStopTime = 0;

            foreach ($historyData as $index => $data) {
                // Skip entries without coordinates
                if (!isset($data['latitude']) || !isset($data['longitude'])) {
                    continue;
                }

                $point = [
                    'lat' => (float)$data['latitude'],
                    'lng' => (float)$data['longitude'],
                    'timestamp' => $data['last_update'] ?? null,
                    'angle' => (float)($data['angle'] ?? 0),
                    'speed' => (float)($data['speed'] ?? 0),
                    'ignition' => $data['239'] ?? null, // ignition status
                    'movement' => $data['240'] ?? null, // movement status
                    'trip_event' => $data['eventID'] ?? null,
                    'trip_status' => isset($data['250']) ? (int)$data['250'] : 0, // trip status
                    'trip_odometer' => getOdometerValue($data) ?? 0, // trip odometer
                    'index' => $index
                ];

                // Primary method: Track trip events for stop duration
                if (isset($data['eventID']) && $data['eventID'] == 250) {
                    if ($point['trip_status'] == 0) { // Vehicle stopped
                        if (!$lastTripStop) { // Only set if not already tracking a stop
                            $lastTripStop = $point;
                            $lastTripStop['totalOdometer'] = $point['trip_odometer']; // Store total distance at stop
                        }
                    } elseif ($point['trip_status'] == 1 && $lastTripStop) { // Vehicle started
                        // Calculate stop duration
                        $duration = calculateStopDuration($lastTripStop['timestamp'], $point['timestamp'], false);
                        $durationInMinutes = $this->getDurationInMinutes($duration);

                        // Only add stops with duration greater than 1 minute
                        if ($duration > 1) {
                            $tripDistance = max(0, ($point['trip_odometer'] - $lastTripStop['trip_odometer']));

                            $stops[] = [
                                'start_time' => $lastTripStop['timestamp'],
                                'end_time' => $point['timestamp'],
                                'duration' => $duration,
                                'duration_minutes' => formatDuration($duration),
                                'location' => [
                                    'lat' => $lastTripStop['lat'],
                                    'lng' => $lastTripStop['lng'],
                                    'address' => getAddressFromCoordinates($lastTripStop['lat'], $lastTripStop['lng'])
                                ],
                                'trip_distance' => round($tripDistance / 1000, 2), // Convert to km
                                'total_distance' => round(($lastTripStop['totalOdometer'] / 1000), 2) // Match frontend format
                            ];
                            $totalStopTime += $duration;
                        }
                        $lastTripStop = null;
                    }
                }
                // Backup method: Also detect stops based on speed and ignition
                elseif ($point['speed'] < 1 && isset($point['ignition']) && $point['ignition'] == 1) {
                    if (!$lastTripStop) {
                        $lastTripStop = $point;
                        $lastTripStop['totalOdometer'] = $point['trip_odometer'];
                    }
                }
                // Vehicle started moving again
                elseif ($point['speed'] > 5 && $lastTripStop) {
                    $duration = calculateStopDuration($lastTripStop['timestamp'], $point['timestamp'], false);
                    $durationInMinutes = $this->getDurationInMinutes($duration);

                    // Only add stops with duration greater than 1 minute
                    if ($duration > 1) {
                        $tripDistance = max(0, ($point['trip_odometer'] - $lastTripStop['trip_odometer']));

                        $stops[] = [
                            'start_time' => $lastTripStop['timestamp'],
                            'end_time' => $point['timestamp'],
                            'duration' => $duration,
                            'duration_minutes' => formatDuration($duration),
                            'location' => [
                                'lat' => $lastTripStop['lat'],
                                'lng' => $lastTripStop['lng'],
                                'address' => getAddressFromCoordinates($lastTripStop['lat'], $lastTripStop['lng'])
                            ],
                            'trip_distance' => round($tripDistance / 1000, 2), // Convert to km
                            'total_distance' => round(($lastTripStop['totalOdometer'] / 1000), 2) // Match frontend format
                        ];
                        $totalStopTime += $duration;
                    }
                    $lastTripStop = null;
                }
            }

            // Handle ongoing stop
            if ($lastTripStop) {
                $duration = calculateStopDuration($lastTripStop['timestamp'], now()->format('d/m/Y H:i:s'), false);
                $durationInMinutes = $this->getDurationInMinutes($duration);

                // Only add ongoing stop if duration is greater than 1 minute
                if ($duration > 1) {
                    $lastData = end($historyData);
                    $currentOdometer = getOdometerValue($lastData) ?? 0;
                    $tripDistance = max(0, ($currentOdometer - $lastTripStop['trip_odometer']));

                    $stops[] = [
                        'start_time' => $lastTripStop['timestamp'],
                        'end_time' => 'Ongoing',
                        'duration' => $duration,
                        'duration_minutes' => formatDuration($duration),
                        'location' => [
                            'lat' => $lastTripStop['lat'],
                            'lng' => $lastTripStop['lng'],
                            'address' => getAddressFromCoordinates($lastTripStop['lat'], $lastTripStop['lng'])
                        ],
                        'trip_distance' => round($tripDistance / 1000, 2), // Convert to km
                        'total_distance' => round(($lastTripStop['totalOdometer'] / 1000), 2) // Match frontend format
                    ];
                    $totalStopTime += $duration;
                }
            }

            $result = [
                'stops' => $stops,
                'total_stop_time' => $totalStopTime
            ];

            // Cache the results for 5 minutes
            cache()->put($cacheKey, $result, 300);

            return $result;
        } catch (\Exception $e) {
            Log::error("Error calculating stops for IMEI {$imei}: " . $e->getMessage());
            return ['stops' => [], 'total_stop_time' => 0];
        }
    }

    /**
     * Get cached history data for a specific date
     */
    private function getCachedHistoryData($imei, $date)
    {
        $cacheKey = self::HISTORY_CACHE_KEY . "{$imei}_{$date}";

        return Cache::remember($cacheKey, self::HISTORY_CACHE_DURATION, function () use ($imei, $date) {
            $historyPath = public_path("data/history/{$imei}/{$date}.json");

            if (!file_exists($historyPath)) {
                return [];
            }

            try {
                $historyData = json_decode(file_get_contents($historyPath), true);
                return $historyData ?? [];
            } catch (\Exception $e) {
                Log::error("Failed to load history data for {$date}: " . $e->getMessage());
                return [];
            }
        });
    }



    /**
     * Get duration in minutes from duration string
     */
    private function getDurationInMinutes($duration)
    {
        if (!$duration || $duration == 'N/A') return 0;

        $hours = 0;
        $minutes = 0;

        if (preg_match('/(\d+)h/', $duration, $matches)) {
            $hours = (int)$matches[1];
        }

        if (preg_match('/(\d+)m/', $duration, $matches)) {
            $minutes = (int)$matches[1];
        }

        return ($hours * 60) + $minutes;
    }

    /**
     * Get vehicle history data for a specific date
     * Includes optimized data structure with stop points for mobile consumption
     */
    public function getVehicleHistory(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'imei' => 'required|string',
            'date' => 'required|date_format:d-m-Y',
        ]);

        if ($validator->fails()) {
            return validationError($validator->errors());
        }

        try {
            $imei = $request->imei;
            $date = $request->date;

            // Check if user has access to this vehicle
            $user = Auth::user();
            $vehicle = Vehicle::where('imei', $imei)
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                })
                ->first();

            if (!$vehicle) {
                return jsonResponse(false, [
                    'message' => __('app.vehicle_not_found'),
                ], 422);
            }

            // Get history data
            $historyData = $this->getCachedHistoryData($imei, $date);

            if (empty($historyData)) {
                return jsonResponse(true, [
                    'message' => __('app.vehicle_history_retrieved'),
                    'history_data' => [],
                    'stops' => [],
                    'total_stops' => 0,
                    'total_stop_time' => 0
                ]);
            }

            // Process history data for mobile consumption
            $processedData = [];
            foreach ($historyData as $data) {
                if (!isset($data['latitude']) || !isset($data['longitude'])) {
                    continue;
                }

                $processedData[] = [
                    'longitude' => (float)$data['longitude'],
                    'latitude' => (float)$data['latitude'],
                    'timestamp' => $data['last_update'] ?? null,
                    'movement_status' => ($data['240'] && $data['speed'] > 1) ?? null,
                    'ignition_status' => (isset($data['239']) && $data['239'] == 1) ?? null,
                    'speed' => (float)($data['speed'] ?? 0),
                    'angle' => (float)($data['angle'] ?? 0),
                    'indicators' => $this->getIndicatorsFromData($data),
                    'odometer' => round(getOdometerValue($data) / 1000),
                    'fuel_used' => getFuelConsumption($data),
                    'average_fuel' => number_format(
                        getAverageFuelValue(getFuelConsumption($data), round(getOdometerValue($data) / 1000), 'l_per_100km'),
                        2
                    ),
                    'fuel_level_percentage' => getFuelLevelPercentage($data),
                    'fuel_level' => getFuelLevelLiters($data),
                ];
            }

            // Calculate stops using the improved method
            $stopsData = $this->calculateTodayStops($imei, $date);

            return jsonResponse(true, [
                'message' => __('app.vehicle_history_retrieved'),
                'history_data' => $processedData,
                'stops' => $stopsData['stops'],
                'total_stops' => count($stopsData['stops']),
                'total_stop_time' => formatDuration($stopsData['total_stop_time'])
            ]);
        } catch (\Exception $e) {
            Log::error('Vehicle history retrieval failed: ' . $e->getMessage());
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }

    /**
     * Get indicators data from raw device data
     */
    private function getIndicatorsFromData($data)
    {
        return [
            'ignition' => isset($data['239']) && $data['239'] == 1,
            'parked' => isset($data['240']) && $data['240'] == 0 && ($vehicleDetails['speed'] ?? 0) == 0,
            'signal' => $data['21'] ?? null
        ];
    }
}
