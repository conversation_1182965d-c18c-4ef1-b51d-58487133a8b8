<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Models\Driver;
use App\Models\Notification;
use App\Models\Vehicle;
use App\Models\VehicleUser;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    public function getStats()
    {
        try {
            $user = Auth::user();
            // Get device data from live.json
            $deviceData = @file_get_contents(public_path('data/live.json'));
            $deviceData = json_decode($deviceData, true);

            $movingCount = 0;
            $parkedCount = 0;
            $stoppedCount = 0;
            $totalFuel = 0;
            $averageFuel = 0;

            if (is_array($deviceData)) {
                foreach ($deviceData as $data) {
                    $movingStatus = $data['240'] ?? null;
                    $ignitionStatus = $data['239'] ?? null;
                    $speed = $data['speed'] ?? null;
                    $totalFuelData = $this->getFuelValue($data);
                    $averageFuelData = $this->getAverageFuelValue($data);

                    $totalFuel += $totalFuelData;
                    $averageFuel += $averageFuelData;

                    if ($movingStatus == 1 && $speed > 0) {
                        $movingCount++;
                    } elseif ($movingStatus == 0 && $ignitionStatus == 0) {
                        $parkedCount++;
                        $stoppedCount++;
                    } elseif ($movingStatus == 0 && $ignitionStatus == 1) {
                        $stoppedCount++;
                    }
                }
            }

            $imeis = array_keys($deviceData ?? []);

            // Get total vehicles count
            $totalVehicles = Vehicle::when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })->where('status', 1)->count();

            // Get drivers count
            $drivers = Driver::when(!$user->can('all_drivers_access'), function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })->where('status', 1)->count();

            // Get anomaly vehicles count
            $anomalyVehicles = Vehicle::when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })->where('icon', 'anomaly')->count();

            // Get vehicles in maintenance count
            $vehiclesInMaintenance = Vehicle::when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
            })->whereHas('events', function($query) {
                $query->where('is_under_maintenance', 1)
                      ->whereIn('maintenance_status', ['pending', 'in progress']);
            })->count();

            // Get vehicles with live data
            $vehicles = Vehicle::with(['driver:id,name,user_id'])
                ->where('status', 1)
                ->whereIn('imei', $imeis)
                ->when(!$user->can('all_vehicles_access'), function ($query) use ($user) {
                    $query->whereIn('id', VehicleUser::where('user_id', $user->id)->pluck('vehicle_id'));
                })
                ->get();

            // Attach live data to vehicles
            $vehicles->each(function ($vehicle) use ($deviceData) {
                $imei = (string) $vehicle->imei;
                $eachDeviceData = $deviceData[$imei] ?? null;

                if ($eachDeviceData) {
                    $vehicle->movement_status = $eachDeviceData['240'] ?? null;
                    $vehicle->ignition_status = $eachDeviceData['239'] ?? null;
                    $vehicle->speed = $eachDeviceData['speed'] ?? null;
                    $vehicle->timestamp = $eachDeviceData['last_update'] ?? null;
                    $vehicle->fuel_level = $this->getFuelValue($eachDeviceData);
                    $vehicle->average_fuel = $this->getAverageFuelValue($eachDeviceData);
                }
            });

            $notifications = Notification::when($user->role != 'admin', function ($query) use ($user) {
                $vehicles = Vehicle::whereHas('vehicleUsers', function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })->pluck('id');
                $query->whereIn('vehicle_id', $vehicles)->orWhere('user_id', $user->id);
            })->latest()->take(10)->get()->map(function ($notification) {
                if ($notification->params) {
                    $params = json_decode($notification->params ?? '', true);
                } else {
                    $params = [];
                }
                return [
                    'title' =>   __('translations.' . $notification->title, $params),
                    'body' =>   __('translations.' . $notification->notification, $params),
                    'created_at' => Carbon::parse($notification->created_at)->diffForHumans(),
                ];
            });

            return jsonResponse(true, [
                'message' => __('app.dashboard_stats_retrieved'),
                'stats' => [
                    'total_vehicles' => $totalVehicles,
                    'drivers' => $drivers,
                    'moving_count' => $movingCount,
                    'parked_count' => $parkedCount,
                    'stopped_count' => $stoppedCount,
                    'total_fuel' => round($totalFuel, 2),
                    'average_fuel' => round($averageFuel, 2),
                    'anomaly_vehicles' => $anomalyVehicles,
                    'vehicles_in_maintenance' => $vehiclesInMaintenance,
                ],
                'vehicles' => $vehicles->map(function ($vehicle) {
                    return [
                        'id' => $vehicle->id,
                        'license_plate' => $vehicle->license_plate,
                        'model' => $vehicle->model,
                        'status' => $vehicle->status,
                        'driver_name' => $vehicle->driver ? $vehicle->driver->name : null,
                        'movement_status' => $vehicle->movement_status ?? null,
                        'ignition_status' => $vehicle->ignition_status ?? null,
                        'speed' => $vehicle->speed ?? 0,
                        'fuel_level' => $vehicle->fuel_level ?? 0,
                        'average_fuel' => $vehicle->average_fuel ?? 0,
                        'last_update' => $vehicle->timestamp ?? null,
                    ];
                }),
                'notifications' => $notifications
            ]);

        } catch (\Exception $e) {
            return jsonResponse(false, [
                'message' => __('app.error_occurred'),
            ], 500);
        }
    }

    private function getFuelValue($data)
    {
        if (isset($data['33'])) {
            return (float) $data['33'];
        } elseif (isset($data['86'])) {
            return (float) $data['86'];
        } elseif (isset($data['12'])) {
            return (float) $data['12'];
        } else {
            return 0;
        }
    }

    private function getAverageFuelValue($data)
    {
        if (isset($data['18'])) {
            return (float) $data['18'];
        } elseif (isset($data['135'])) {
            return (float) $data['135'];
        } elseif (isset($data['110'])) {
            return (float) $data['110'];
        } elseif (isset($data['60'])) {
            return (float) $data['60'];
        } elseif (isset($data['13'])) {
            return (float) $data['13'];
        } else {
            return 0;
        }
    }
}
