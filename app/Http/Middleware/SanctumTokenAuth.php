<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\PersonalAccessToken;
use Symfony\Component\HttpFoundation\Response;

class SanctumTokenAuth
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
       

        // Check if user is already authenticated
        if (Auth::check()) {
            return $next($request);
        }

        // Check for token in URL parameters
        $token = $request->query('token');

        if ($token) {
            try {
                // Find the personal access token
                $accessToken = PersonalAccessToken::findToken($token);

                if ($accessToken && $accessToken->tokenable) {
                    // Check if the user is active
                    if (isset($accessToken->tokenable->is_active) && $accessToken->tokenable->is_active == 0) {
                        return redirect()->route('login')->with('error', 'Account is not active.');
                    }

                    // Log in the user using the web guard
                    Auth::guard('web')->login($accessToken->tokenable);

                    // Remove token from URL to clean it up
                    $cleanUrl = $request->url();
                    $queryParams = $request->query();
                    unset($queryParams['token']);

                    if (!empty($queryParams)) {
                        $cleanUrl .= '?' . http_build_query($queryParams);
                    }

                    // Redirect to clean URL without token
                    return redirect($cleanUrl);
                }
            } catch (\Exception $e) {
                // Invalid token, continue to login redirect
                Log::error('Invalid token used for authentication: ' . $e->getMessage());
            }
        }

        // No valid token found, redirect to login
        return redirect()->route('login');
    }
}
