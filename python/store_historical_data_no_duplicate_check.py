# Alternative version of store_historical_data WITHOUT duplicate checking
# Use this if you want to completely remove duplicate logic for maximum performance

def store_historical_data_no_duplicates(io_dict, device_imei):
    """
    ULTRA-OPTIMIZED: Store historical data without duplicate checking.
    Maximum performance for high-frequency GPS updates.
    """
    # Static cache for historical data to reduce disk reads/writes
    if not hasattr(store_historical_data_no_duplicates, "daily_data_cache"):
        store_historical_data_no_duplicates.daily_data_cache = {}
        store_historical_data_no_duplicates.dates_cache = {}
        store_historical_data_no_duplicates.last_write_time = {}
        store_historical_data_no_duplicates.write_interval = 5.0  # <PERSON><PERSON> writes every 5 seconds

    history_path = f"{BASE_PATH}data/history/{device_imei}"
    date_file = "dates.json"

    italy_timezone = pytz.timezone('Europe/Rome')
    current_date = datetime.datetime.now(italy_timezone).strftime('%d-%m-%Y')
    data_file = f"{current_date}.json"

    if not os.path.exists(history_path):
        os.makedirs(history_path)

    # Create a cache key for this device and date
    cache_key = f"{device_imei}_{current_date}"
    current_time = time.time()

    # Initialize cache entry if needed
    if cache_key not in store_historical_data_no_duplicates.last_write_time:
        store_historical_data_no_duplicates.last_write_time[cache_key] = 0

    # Update dates.json with error recovery (using cache)
    date_path = os.path.join(history_path, date_file)

    # Load dates from cache or file
    if device_imei not in store_historical_data_no_duplicates.dates_cache:
        dates = set()
        if os.path.exists(date_path):
            try:
                with open(date_path, "r") as file:
                    dates = set(json.load(file))
            except json.JSONDecodeError:
                print("Dates file corrupted, attempting repair...")
                dates = set()
        store_historical_data_no_duplicates.dates_cache[device_imei] = dates

    # Update dates cache
    store_historical_data_no_duplicates.dates_cache[device_imei].add(current_date)

    # Only write dates file if enough time has passed
    if current_time - store_historical_data_no_duplicates.last_write_time.get(f"{device_imei}_dates", 0) >= store_historical_data_no_duplicates.write_interval:
        sorted_dates = sorted(store_historical_data_no_duplicates.dates_cache[device_imei],
                             key=lambda d: datetime.datetime.strptime(d, '%d-%m-%Y'),
                             reverse=True)

        # Write dates atomically
        temp_date_path = date_path + ".tmp"
        try:
            with open(temp_date_path, "w") as file:
                json.dump(sorted_dates, file, separators=(',', ':'))
            os.replace(temp_date_path, date_path)
            store_historical_data_no_duplicates.last_write_time[f"{device_imei}_dates"] = current_time
        except Exception as e:
            print(f"Error writing dates file: {e}")
            if os.path.exists(temp_date_path):
                os.remove(temp_date_path)

    # Update daily data file with error recovery (using cache)
    data_path = os.path.join(history_path, data_file)

    try:
        # Load daily data from cache or file
        if cache_key not in store_historical_data_no_duplicates.daily_data_cache:
            daily_data = []
            if os.path.exists(data_path):
                try:
                    with open(data_path, "r") as file:
                        content = file.read().strip()
                        if content:
                            daily_data = json.loads(content)
                            if not isinstance(daily_data, list):
                                daily_data = []
                except json.JSONDecodeError:
                    print("Daily data file corrupted, attempting repair...")
                    daily_data = repair_json_file(data_path, expect_list=True)

            # Ensure daily_data is a list
            if not isinstance(daily_data, list):
                daily_data = []

            store_historical_data_no_duplicates.daily_data_cache[cache_key] = daily_data

        # Sanitize input data
        sanitized_io_dict = sanitize_dict(io_dict)

        # Skip if no valid data
        if not sanitized_io_dict:
            return

        # NO DUPLICATE CHECKING - Always store the data
        store_historical_data_no_duplicates.daily_data_cache[cache_key].append(sanitized_io_dict)

        # Only write to disk if enough time has passed
        if current_time - store_historical_data_no_duplicates.last_write_time.get(cache_key, 0) >= store_historical_data_no_duplicates.write_interval:
            # Write daily data atomically
            temp_data_path = data_path + ".tmp"
            try:
                with open(temp_data_path, "w") as file:
                    json.dump(store_historical_data_no_duplicates.daily_data_cache[cache_key], file, separators=(',', ':'))
                os.replace(temp_data_path, data_path)
                store_historical_data_no_duplicates.last_write_time[cache_key] = current_time
            except Exception as e:
                print(f"Error writing daily data file: {e}")
                if os.path.exists(temp_data_path):
                    os.remove(temp_data_path)

    except Exception as e:
        print(f"Error in store_historical_data_no_duplicates: {e}")

    # Clean up old cache entries to prevent memory leaks
    # Keep only entries from the last 2 days
    current_time = time.time()
    for key in list(store_historical_data_no_duplicates.last_write_time.keys()):
        if current_time - store_historical_data_no_duplicates.last_write_time[key] > 172800:  # 48 hours
            if key in store_historical_data_no_duplicates.daily_data_cache:
                del store_historical_data_no_duplicates.daily_data_cache[key]
            del store_historical_data_no_duplicates.last_write_time[key]
